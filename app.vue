<template>
  <a-config-provider :locale="locale">
    <NuxtPage></NuxtPage>
  </a-config-provider>
</template>
<script lang="ts" setup>
  import { ConfigProvider } from "ant-design-vue";
  import zhCN from "ant-design-vue/es/locale/zh_CN";
  useHead({
    title: "UAR管理系统",
  });
  const locale = zhCN;
</script>
<style>
  html,
  body {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
  }

  #__nuxt {
    width: 100%;
    height: 100%;
  }
</style>
