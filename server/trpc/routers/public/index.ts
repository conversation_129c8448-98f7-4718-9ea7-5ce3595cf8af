import { singleResultProcedure, router } from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { useJwt } from "@/server/utils/jwt";
import svgCaptcha from "svg-captcha";
import { useApiFace } from "@/server/utils/face";
import { useSession } from "@/server/utils/session";
const login = singleResultProcedure
  .meta({ authRequired: false, permission: [] })
  .input(
    z.object({
      username: z.string(),
      password: z.string(),
      verifycode: z.string(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    // 使用新的session工具函数验证验证码
    const session = useSession(ctx.event);
    if (!session.verifyCaptcha(input.verifycode)) {
      return { code: 0, message: "验证码错误或已过期", data: null };
    }

    const user = await prisma.user.login(input.username, input.password);
    if (!user) {
      return { code: 0, message: "用户名或密码错误", data: null };
    }

    // 登录成功后，设置用户会话信息
    session.setUser({
      id: user.id,
      username: user.username,
    });

    // 生成JWT令牌
    const token = useJwt().sign(user);
    setCookie(ctx.event, "jwt", token, {
      httpOnly: true,
      sameSite: "lax",
    });

    return { code: 1, data: user, message: "登录成功" };
  });

const faceLogin = singleResultProcedure
  .meta({ authRequired: false, permission: [] })
  .input(
    z.object({
      faceDescriptor: z.array(z.number()),
      faceImage: z.string(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    // 人脸特征匹配逻辑
    const { searchFace } = useApiFace();
    const res = await searchFace(input.faceImage);

    if (
      res.error_code === 0 &&
      res.result.user_list &&
      res.result.user_list.length > 0
    ) {
      const user = await prisma.user.findFirst({
        include: {
          role: true,
        },
        where: {
          username: res.result.user_list[0].user_id,
        },
      });
      if (user) {
        // 使用新的session工具函数设置用户会话信息
        const session = useSession(ctx.event);
        session.setUser({
          id: user.id,
          username: user.username,
        });

        // 生成JWT令牌
        const token = useJwt().sign(user);
        setCookie(ctx.event, "jwt", token, {
          httpOnly: true,
          sameSite: "lax",
        });

        return { code: 1, data: { user, apiRes: res }, message: "登录成功" };
      }
    }
    return {
      code: 0,
      message: "人脸识别失败,请确保进行了人脸采集",
      data: null,
    };
  });

const captcha = singleResultProcedure
  .meta({ authRequired: false, permission: [] })
  .input(
    z.object({
      w: z.number().default(80),
      h: z.number().default(30),
      expiryInSeconds: z.number().default(300), // 验证码过期时间，默认5分钟
    })
  )
  .query(async ({ input, ctx }) => {
    const captcha = svgCaptcha.create({
      //反转颜色
      inverse: false,
      fontSize: 36,
      ignoreChars: "oO0iI1lL7zZ",
      //噪声条纹
      noise: 0,
      width: input.w,
      height: input.h,
    });

    // 使用新的session工具函数设置验证码
    const session = useSession(ctx.event);
    session.setCaptcha(captcha.text, input.expiryInSeconds);

    const encodeData = Buffer.from(captcha.data).toString("base64");
    return {
      code: 1,
      message: "success",
      data: `data:image/svg+xml;base64,${encodeData}`,
    };
  });
const status = singleResultProcedure.query(async ({ ctx }) => {
  if (ctx.user) {
    const user = await prisma.user.findUnique({
      omit: {
        password: true,
      },
      where: {
        id: ctx.user.id,
      },
    });
    return {
      code: 1,
      message: "success",
      data: user,
    };
  }
  throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
});
const menu = singleResultProcedure.query(async ({ ctx }) => {
  if (ctx.user) {
    const menu = await prisma.menu.findMany({
      include: {
        RoleMenu: true,
      },
      where: {
        RoleMenu: {
          some: {
            role_id: ctx.user?.role_id,
          },
        },
      },
    });
    return { code: 1, message: "success", data: menu };
  }
  throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
});
const logout = singleResultProcedure.query(async ({ ctx }) => {
  // 删除JWT cookie
  deleteCookie(ctx.event, "jwt");

  // 清除session中的用户信息
  const session = useSession(ctx.event);
  session.unset("user");

  return { code: 1, message: "登出成功" };
});

export const testRouter = router({
  auth: router({
    login,
    faceLogin,
    captcha,
    status,
    logout,
    menu,
  }),
});
