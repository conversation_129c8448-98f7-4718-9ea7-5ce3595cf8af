import {
  router,
  mergeRouters,
  singleResultProcedure,
  tableResultProcedure,
} from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";

// 查询盘点单列表
export const queryStockCheckList = tableResultProcedure
  .meta({ permission: ["stockcheck:query"], authRequired: true })
  .input(
    z.object({
      take: z.number().optional(),
      skip: z.number().optional(),
      checkNo: z.string().optional(),
      status: z.string().optional(),
      warehouseId: z.number().optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
    })
  )
  .query(async ({ input }) => {
    const where: any = {};

    if (input.checkNo) {
      where.checkNo = { contains: input.checkNo };
    }

    if (input.status) {
      where.status = input.status;
    }

    if (input.warehouseId) {
      where.warehouse_id = input.warehouseId;
    }

    if (input.startDate || input.endDate) {
      where.plannedDate = {};
      if (input.startDate) {
        where.plannedDate.gte = new Date(input.startDate);
      }
      if (input.endDate) {
        where.plannedDate.lte = new Date(input.endDate);
      }
    }

    const stockChecks = await prisma.stockCheck.findManyWithCount({
      where,
      take: input.take,
      skip: input.skip,
      include: {
        warehouse: true,
        checkUser: {
          select: {
            id: true,
            name: true,
          },
        },
        approveUser: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            items: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return {
      code: 1,
      message: "success",
      data: stockChecks,
    };
  });

// 创建盘点单
export const createStockCheck = singleResultProcedure
  .meta({ permission: ["stockcheck:create"], authRequired: true })
  .input(
    z.object({
      title: z.string().min(1, "盘点标题不能为空"),
      warehouse_id: z.number().min(1, "请选择仓库"),
      checkType: z.enum(["full", "partial"]).default("full"),
      plannedDate: z.string().min(1, "请选择计划盘点日期"),
      note: z.string().optional(),
      materialIds: z.array(z.number()).optional(), // 抽盘时选择的物料ID
    })
  )
  .mutation(async ({ input, ctx }) => {
    const userId = ctx.user?.id;
    if (!userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "用户未登录",
      });
    }

    // 检查仓库是否存在
    const warehouse = await prisma.warehouse.findUnique({
      where: { id: input.warehouse_id },
    });
    if (!warehouse) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "仓库不存在",
      });
    }

    // 生成盘点单号
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10).replace(/-/g, "");
    const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, "");
    const checkNo = `PD${dateStr}${timeStr}`;

    // 获取库存数据
    let stockWhere: any = {
      warehouse_id: input.warehouse_id,
      quantity: { gt: 0 }, // 只盘点有库存的物料
    };

    // 如果是抽盘，只盘点指定的物料
    if (input.checkType === "partial" && input.materialIds?.length) {
      stockWhere.materiel_id = { in: input.materialIds };
    }

    const stockItems = await prisma.stock.findMany({
      where: stockWhere,
      include: {
        materiel: true,
      },
    });

    if (stockItems.length === 0) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "该仓库没有可盘点的库存",
      });
    }

    // 创建盘点单和盘点明细
    const result = await prisma.$transaction(async (tx) => {
      // 创建盘点单
      const stockCheck = await tx.stockCheck.create({
        data: {
          checkNo,
          title: input.title,
          warehouse_id: input.warehouse_id,
          checkType: input.checkType,
          plannedDate: new Date(input.plannedDate),
          checkUser_id: userId,
          note: input.note,
          totalItems: stockItems.length,
        },
      });

      // 创建盘点明细
      const checkItems = stockItems.map((stock) => ({
        stockCheck_id: stockCheck.id,
        materiel_id: stock.materiel_id,
        batch_no: stock.batch_no,
        location: stock.location,
        systemQuantity: stock.quantity,
      }));

      await tx.stockCheckItem.createMany({
        data: checkItems,
      });

      return stockCheck;
    });

    return {
      code: 1,
      message: "盘点单创建成功",
      data: result,
    };
  });

// 查询盘点单详情
export const getStockCheckDetail = singleResultProcedure
  .meta({ permission: ["stockcheck:query"], authRequired: true })
  .input(z.object({ id: z.number() }))
  .query(async ({ input }) => {
    const stockCheck = await prisma.stockCheck.findUnique({
      where: { id: input.id },
      include: {
        warehouse: true,
        checkUser: {
          select: {
            id: true,
            name: true,
          },
        },
        approveUser: {
          select: {
            id: true,
            name: true,
          },
        },
        items: {
          include: {
            materiel: true,
            checkUser: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: {
            id: "asc",
          },
        },
      },
    });

    if (!stockCheck) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "盘点单不存在",
      });
    }

    return {
      code: 1,
      message: "success",
      data: stockCheck,
    };
  });

// 更新盘点明细
export const updateStockCheckItem = singleResultProcedure
  .meta({ permission: ["stockcheck:update"], authRequired: true })
  .input(
    z.object({
      id: z.number(),
      actualQuantity: z.number().min(0, "实盘数量不能为负数"),
      note: z.string().optional(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    const userId = ctx.user?.id;
    if (!userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "用户未登录",
      });
    }

    // 查找盘点明细
    const checkItem = await prisma.stockCheckItem.findUnique({
      where: { id: input.id },
      include: {
        stockCheck: true,
      },
    });

    if (!checkItem) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "盘点明细不存在",
      });
    }

    // 检查盘点单状态
    if (
      checkItem.stockCheck.status !== "draft" &&
      checkItem.stockCheck.status !== "checking"
    ) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "盘点单状态不允许修改",
      });
    }

    // 计算差异数量
    const differenceQuantity =
      input.actualQuantity - Number(checkItem.systemQuantity);

    // 更新盘点明细
    const result = await prisma.$transaction(async (tx) => {
      const updatedItem = await tx.stockCheckItem.update({
        where: { id: input.id },
        data: {
          actualQuantity: input.actualQuantity,
          differenceQuantity,
          status: "checked",
          note: input.note,
          checkUser_id: userId,
          checkedAt: new Date(),
        },
      });

      // 更新盘点单统计
      const stockCheck = await tx.stockCheck.findUnique({
        where: { id: checkItem.stockCheck_id },
        include: {
          items: true,
        },
      });

      if (stockCheck) {
        const checkedItems = stockCheck.items.filter(
          (item) => item.status === "checked"
        ).length;
        const differenceItems = stockCheck.items.filter(
          (item) =>
            item.differenceQuantity !== null &&
            Number(item.differenceQuantity) !== 0
        ).length;

        await tx.stockCheck.update({
          where: { id: checkItem.stockCheck_id },
          data: {
            checkedItems,
            differenceItems,
            status:
              checkedItems === stockCheck.totalItems ? "completed" : "checking",
            actualDate:
              checkedItems === stockCheck.totalItems
                ? new Date()
                : stockCheck.actualDate,
          },
        });
      }

      return updatedItem;
    });

    return {
      code: 1,
      message: "盘点明细更新成功",
      data: result,
    };
  });

// 完成盘点并调整库存
export const completeStockCheck = singleResultProcedure
  .meta({ permission: ["stockcheck:complete"], authRequired: true })
  .input(z.object({ id: z.number() }))
  .mutation(async ({ input, ctx }) => {
    const userId = ctx.user?.id;
    if (!userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "用户未登录",
      });
    }

    // 查找盘点单
    const stockCheck = await prisma.stockCheck.findUnique({
      where: { id: input.id },
      include: {
        items: {
          include: {
            materiel: true,
          },
        },
      },
    });

    if (!stockCheck) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "盘点单不存在",
      });
    }

    if (stockCheck.status !== "completed") {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "盘点单未完成，无法调整库存",
      });
    }

    // 调整库存
    const result = await prisma.$transaction(async (tx) => {
      for (const item of stockCheck.items) {
        if (item.differenceQuantity && Number(item.differenceQuantity) !== 0) {
          // 更新库存记录
          await tx.stock.update({
            where: {
              materiel_id_warehouse_id_batch_no: {
                materiel_id: item.materiel_id,
                warehouse_id: stockCheck.warehouse_id,
                batch_no: item.batch_no,
              },
            },
            data: {
              quantity: Number(item.actualQuantity),
            },
          });

          // 更新物料总库存
          await tx.materiel.update({
            where: { id: item.materiel_id },
            data: {
              stock: { increment: Number(item.differenceQuantity) },
            },
          });

          // 更新盘点明细状态
          await tx.stockCheckItem.update({
            where: { id: item.id },
            data: {
              status: "adjusted",
            },
          });
        }
      }

      // 更新盘点单状态
      const updatedStockCheck = await tx.stockCheck.update({
        where: { id: input.id },
        data: {
          status: "completed",
          approveUser_id: userId,
          approvedAt: new Date(),
        },
      });

      return updatedStockCheck;
    });

    return {
      code: 1,
      message: "库存调整完成",
      data: result,
    };
  });

export default mergeRouters(
  router({
    queryStockCheckList,
    createStockCheck,
    getStockCheckDetail,
    updateStockCheckItem,
    completeStockCheck,
  })
);
