import {
  router,
  mergeRouters,
  singleResultProcedure,
  tableResultProcedure,
} from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { WarehouseType } from "@prisma/client";
export const queryWarehouseList = tableResultProcedure
  .meta({
    permission: ["warehouse:query"],
    authRequired: true,
  })
  .input(
    z.object({
      take: z.number().optional(),
      skip: z.number().optional(),
      type: z.array(z.nativeEnum(WarehouseType)).optional(),
    })
  )
  .query(async ({ input }) => {
    const warehouse = await prisma.warehouse.findManyWithCount({
      where: {
        type: {
          in: input.type,
        },
      },
    });
    return { code: 1, message: "success", data: warehouse };
  });

export const addWarehouse = singleResultProcedure
  .input(
    z.object({
      name: z.string(),
      address: z.string(),
      lock: z.boolean(),
      type: z.nativeEnum(WarehouseType),
    })
  )
  .mutation(async ({ input }) => {
    const warehouse = await prisma.warehouse.create({
      data: input,
    });
    return { code: 1, message: "success", data: warehouse };
  });

export const updateWarehouse = singleResultProcedure
  .input(
    z.object({
      id: z.number(),
      name: z.string(),
      address: z.string(),
      lock: z.boolean(),
      type: z.nativeEnum(WarehouseType),
    })
  )
  .mutation(async ({ input }) => {
    const warehouse = await prisma.warehouse.update({
      where: { id: Number(input.id) },
      data: {
        name: input.name,
        address: input.address,
        lock: input.lock,
        type: input.type,
      },
    });
    return { code: 1, message: "success", data: warehouse };
  });
