import {
  router,
  singleResultProcedure,
} from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";

// 获取系统配置
export const getSystemConfig = singleResultProcedure
  .meta({ permission: ["system:query"], authRequired: true })
  .query(async () => {
    try {
      const system = await prisma.system.findFirst();
      
      if (!system) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "系统配置不存在",
        });
      }

      return {
        code: 1,
        message: "获取系统配置成功",
        data: system,
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "获取系统配置失败",
        cause: error,
      });
    }
  });

// 更新系统配置
export const updateSystemConfig = singleResultProcedure
  .meta({ permission: ["system:update"], authRequired: true })
  .input(
    z.object({
      checkAble: z.boolean().optional(),
      checkInitAble: z.boolean().optional(),
    })
  )
  .mutation(async ({ input }) => {
    try {
      const system = await prisma.system.findFirst();
      
      if (!system) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "系统配置不存在",
        });
      }

      const updatedSystem = await prisma.system.update({
        where: { id: system.id },
        data: {
          ...input,
        },
      });

      return {
        code: 1,
        message: "更新系统配置成功",
        data: updatedSystem,
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "更新系统配置失败",
        cause: error,
      });
    }
  });

// 合并所有系统相关路由
export const systemRouter = router({
  getSystemConfig,
  updateSystemConfig,
});
