import {
  router,
  tableResultProcedure,
  singleResultProcedure,
} from "@/server/trpc";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { bomCreateSchema, bomUpdateSchema } from "~/schemas/bom";

export const createBom = singleResultProcedure
  .meta({
    permission: ["bom:create"],
    authRequired: true,
  })
  .input(bomCreateSchema)
  .mutation(async ({ input }) => {
    const { materielId, bom, subProduct } = input;
    const materiel = await prisma.materiel.findUnique({
      where: { id: materielId },
    });
    if (!materiel) {
      return { code: 0, message: "物料不存在" };
    }
    const bomList = await prisma.materielBom.createMany({
      data: bom.map((item) => ({
        parentId: materielId,
        childId: item.materielId,
        quantity: item.quantity,
        remark: item.note,
      })),
    });
    const subBomList = await prisma.materielSubBom.createMany({
      data: subProduct.map((item) => ({
        parentId: materielId,
        childId: item.materielId,
        quantity: item.quantity,
      })),
    });
    return {
      code: 1,
      message: "BOM创建成功",
      data: { materiel, bomList, subBomList },
    };
  });

export const queryBom = tableResultProcedure
  .meta({
    permission: ["bom:query"],
    authRequired: true,
  })
  .input(
    z.object({
      materielId: z.number(),
    })
  )
  .query(async ({ input }) => {
    const { materielId } = input;
    const bomList = await prisma.materielBom.findManyWithCount({
      where: {
        parentId: materielId,
      },
      include: {
        child: true,
      },
    });
    return { code: 1, message: "BOM查询成功", data: bomList };
  });
export const querySubBom = tableResultProcedure
  .meta({
    permission: ["bom:query"],
    authRequired: true,
  })
  .input(z.object({ materielId: z.number() }))
  .query(async ({ input }) => {
    const { materielId } = input;
    const subBomList = await prisma.materielSubBom.findManyWithCount({
      where: {
        parentId: materielId,
      },
      include: {
        child: true,
      },
    });
    return { code: 1, message: "副产物BOM查询成功", data: subBomList };
  });

export const updateBom = singleResultProcedure
  .meta({
    permission: ["bom:update"],
    authRequired: true,
  })
  .input(bomUpdateSchema)
  .mutation(async ({ input }) => {
    const { materielId, bom, subProduct } = input;
    const materiel = await prisma.materiel.findUnique({
      where: { id: materielId },
    });
    if (!materiel) {
      return { code: 0, message: "物料不存在" };
    }
    await prisma.materielBom.deleteMany({
      where: {
        parentId: materielId,
      },
    });
    await prisma.materielSubBom.deleteMany({
      where: {
        parentId: materielId,
      },
    });
    const bomList = await prisma.materielBom.createMany({
      data: bom.map((item) => ({
        parentId: materielId,
        childId: item.materielId,
        quantity: item.quantity,
        remark: item.note,
      })),
    });
    const subBomList = await prisma.materielSubBom.createMany({
      data: subProduct.map((item) => ({
        parentId: materielId,
        childId: item.materielId,
        quantity: item.quantity,
      })),
    });
    return {
      code: 1,
      message: "BOM更新成功",
      data: { materiel, bomList, subBomList },
    };
  });
