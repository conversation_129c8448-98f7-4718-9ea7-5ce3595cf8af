import {
  router,
  tableResultProcedure,
  singleResultProcedure,
} from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { prisma, Prisma } from "@/lib/prisma";
import {
  taskWorkOrderQuerySchema,
  taskWorkOrderCreateSchema,
  taskWorkOrderUpdateSchema,
  taskWorkOrderStatusUpdateSchema,
} from "~/schemas/taskworkorder";

// 查询任务工单列表
export const queryTaskWorkOrder = tableResultProcedure
  .meta({ permission: ["taskworkorder:query"], authRequired: true })
  .input(taskWorkOrderQuerySchema)
  .query(async ({ input }) => {
    try {
      const { title, type, status, priority, quick } = input;
      
      const where: Prisma.TaskWorkOrderWhereInput = {};

      if (title) where.title = { contains: title };
      if (type) where.type = type;
      if (status) where.status = status;
      if (priority) where.priority = priority;
      
      // 快速搜索
      if (quick) {
        where.OR = [
          { title: { contains: quick } },
          { description: { contains: quick } },
          { type: { contains: quick } },
        ];
      }

      const taskWorkOrders = await prisma.taskWorkOrder.findManyWithCount({
        where,
        skip: input.skip,
        take: input.take,
        include: {
          createUser: {
            select: {
              id: true,
              name: true,
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
            },
          },
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return {
        code: 1,
        message: "success",
        data: taskWorkOrders,
      };
    } catch (error) {
      console.error("Query task work order error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "查询任务工单失败",
      });
    }
  });

// 获取任务工单详情
export const getTaskWorkOrder = singleResultProcedure
  .meta({ permission: ["taskworkorder:query"], authRequired: true })
  .input(z.object({ id: z.number() }))
  .query(async ({ input }) => {
    try {
      const taskWorkOrder = await prisma.taskWorkOrder.findUnique({
        where: { id: input.id },
        include: {
          createUser: {
            select: {
              id: true,
              name: true,
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
            },
          },
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      if (!taskWorkOrder) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "任务工单不存在",
        });
      }

      return {
        code: 1,
        message: "success",
        data: taskWorkOrder,
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;
      
      console.error("Get task work order error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "获取任务工单详情失败",
      });
    }
  });

// 创建任务工单
export const createTaskWorkOrder = singleResultProcedure
  .meta({ permission: ["taskworkorder:create"], authRequired: true })
  .input(taskWorkOrderCreateSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      const { participants, ...taskData } = input;

      const taskWorkOrder = await prisma.taskWorkOrder.create({
        data: {
          ...taskData,
          createuser_id: ctx.user.id,
          participants: {
            create: participants.map((userId) => ({
              user_id: userId,
              role: "participant",
            })),
          },
        },
        include: {
          createUser: {
            select: {
              id: true,
              name: true,
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
            },
          },
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      return {
        code: 1,
        message: "创建任务工单成功",
        data: taskWorkOrder,
      };
    } catch (error) {
      console.error("Create task work order error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "创建任务工单失败",
      });
    }
  });

// 更新任务工单
export const updateTaskWorkOrder = singleResultProcedure
  .meta({ permission: ["taskworkorder:update"], authRequired: true })
  .input(taskWorkOrderUpdateSchema)
  .mutation(async ({ input }) => {
    try {
      const { id, participants, ...updateData } = input;

      // 检查任务工单是否存在
      const existingTask = await prisma.taskWorkOrder.findUnique({
        where: { id },
      });

      if (!existingTask) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "任务工单不存在",
        });
      }

      // 使用事务更新任务工单和参与人员
      const updatedTask = await prisma.$transaction(async (tx) => {
        // 更新任务工单基本信息
        const task = await tx.taskWorkOrder.update({
          where: { id },
          data: updateData,
        });

        // 如果提供了参与人员列表，则更新参与人员
        if (participants) {
          // 删除现有参与人员
          await tx.taskWorkOrderUsers.deleteMany({
            where: { task_id: id },
          });

          // 添加新的参与人员
          if (participants.length > 0) {
            await tx.taskWorkOrderUsers.createMany({
              data: participants.map((userId) => ({
                task_id: id,
                user_id: userId,
                role: "participant",
              })),
            });
          }
        }

        // 返回完整的任务工单信息
        return await tx.taskWorkOrder.findUnique({
          where: { id },
          include: {
            createUser: {
              select: {
                id: true,
                name: true,
              },
            },
            assignee: {
              select: {
                id: true,
                name: true,
              },
            },
            participants: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        });
      });

      return {
        code: 1,
        message: "更新任务工单成功",
        data: updatedTask,
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;
      
      console.error("Update task work order error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "更新任务工单失败",
      });
    }
  });

// 删除任务工单
export const deleteTaskWorkOrder = singleResultProcedure
  .meta({ permission: ["taskworkorder:delete"], authRequired: true })
  .input(z.object({ id: z.number() }))
  .mutation(async ({ input }) => {
    try {
      const { id } = input;

      // 检查任务工单是否存在
      const existingTask = await prisma.taskWorkOrder.findUnique({
        where: { id },
      });

      if (!existingTask) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "任务工单不存在",
        });
      }

      // 检查是否可以删除（例如：只有草稿状态的任务可以删除）
      if (existingTask.status !== "draft") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "只有草稿状态的任务工单可以删除",
        });
      }

      await prisma.taskWorkOrder.delete({
        where: { id },
      });

      return {
        code: 1,
        message: "删除任务工单成功",
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;
      
      console.error("Delete task work order error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "删除任务工单失败",
      });
    }
  });

// 更新任务工单状态
export const updateTaskWorkOrderStatus = singleResultProcedure
  .meta({ permission: ["taskworkorder:update"], authRequired: true })
  .input(taskWorkOrderStatusUpdateSchema)
  .mutation(async ({ input }) => {
    try {
      const { id, status, actualStartAt, actualEndAt, actualDuration } = input;

      // 检查任务工单是否存在
      const existingTask = await prisma.taskWorkOrder.findUnique({
        where: { id },
      });

      if (!existingTask) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "任务工单不存在",
        });
      }

      // 状态转换验证
      const validTransitions: Record<string, string[]> = {
        draft: ["pending", "cancelled"],
        pending: ["in_progress", "cancelled"],
        in_progress: ["completed", "cancelled"],
        completed: [],
        cancelled: ["draft", "pending"],
      };

      if (!validTransitions[existingTask.status]?.includes(status)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `无法从${existingTask.status}状态转换到${status}状态`,
        });
      }

      // 准备更新数据
      const updateData: any = { status };

      // 根据状态自动设置时间
      if (status === "in_progress" && !existingTask.actualStartAt) {
        updateData.actualStartAt = actualStartAt || new Date();
      }

      if (status === "completed") {
        updateData.actualEndAt = actualEndAt || new Date();

        // 计算实际时长
        if (existingTask.actualStartAt && updateData.actualEndAt) {
          const startTime = new Date(existingTask.actualStartAt);
          const endTime = new Date(updateData.actualEndAt);
          const durationMs = endTime.getTime() - startTime.getTime();
          updateData.actualDuration = actualDuration || Math.round(durationMs / (1000 * 60 * 60) * 100) / 100; // 转换为小时，保留2位小数
        }
      }

      const updatedTask = await prisma.taskWorkOrder.update({
        where: { id },
        data: updateData,
        include: {
          createUser: {
            select: {
              id: true,
              name: true,
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
            },
          },
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      return {
        code: 1,
        message: "更新任务工单状态成功",
        data: updatedTask,
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Update task work order status error:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "更新任务工单状态失败",
      });
    }
  });

export default router({
  queryTaskWorkOrder,
  getTaskWorkOrder,
  createTaskWorkOrder,
  updateTaskWorkOrder,
  updateTaskWorkOrderStatus,
  deleteTaskWorkOrder,
});
