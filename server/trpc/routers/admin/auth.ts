import {
  router,
  tableResultProcedure,
  singleResultProcedure,
} from "@/server/trpc";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { bomCreateSchema, bomUpdateSchema } from "~/schemas/bom";

export const queryUserMenu = singleResultProcedure
  .meta({
    permission: ["user:query"],
    authRequired: true,
  })
  .query(async ({ ctx }) => {
    const user = await prisma.user.findUnique({
      where: {
        id: ctx.user.id,
      },
      select: {
        role_id: true,
      },
    });
    if (!user) {
      throw new Error("用户不存在");
    }
    const menus = await prisma.role.findFirst({
      where: {
        id: user.role_id,
      },
      select: {
        Menu: true,
      },
    });
    return { code: 1, message: "success", data: menus };
  });
