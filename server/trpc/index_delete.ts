// ~/server/trpc/index.ts
import * as trpc from "@trpc/server";
import type { OnErrorPayload } from "trpc-nuxt/api";
import type { H3Event } from "h3";
import type { inferAsyncReturnType } from "@trpc/server";
import { publicRouter } from "./routes/public/index";
import { adminRouter } from "./routes/admin/index";
export const createContext = (event: H3Event) => {
  function useCheckLogin(event: H3Event) {
    const token = getCookie(event, "jwt");
    if (token) {
      return useJwt().verify(token);
    } else {
      return undefined;
    }
  }
  const user = useCheckLogin(event);
  const session = event.context.session;
  return { event, user, session };
};
export type Context = inferAsyncReturnType<typeof createContext>;

export const router = trpc
  .router<trpc.inferAsyncReturnType<typeof createContext>>()
  .merge("public.", publicRouter)
  .middleware(({ ctx, next }) => {
    if (!ctx.user) {
      //   throw new TRPCError({ code: "UNAUTHORIZED" });
      throw createError({
        message: "未登录",
        status: 401,
        statusCode: 401,
      });
    }
    return next({ ctx });
  })
  .merge("admin.", adminRouter);

export const responseMeta = () => {
  return {
    // headers: {
    // }
  };
};

export const onError = (payload: OnErrorPayload<typeof router>) => {
  console.log(payload.error.message);
};
