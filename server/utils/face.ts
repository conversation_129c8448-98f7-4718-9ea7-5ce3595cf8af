import axios from "axios";

const AK = "w5XaExl5zbsq7B7DErIXiNqa";
const SK = "NvQGtDISjqyxZzHxn3IGQORazvVneLuG";
const URL_GET_ACCESS_TOKEN = "https://aip.baidubce.com/oauth/2.0/token";
const URL_FACE_REGEDIT =
  "https://aip.baidubce.com/rest/2.0/face/v3/faceset/user/add";
const URL_FACE_SEARCH = "https://aip.baidubce.com/rest/2.0/face/v3/search";

// 图片验证相关常量
const MAX_IMAGE_SIZE = 2 * 1024 * 1024; // 2MB
const MIN_FACE_SIZE = 100; // 最小人脸像素
const VALID_IMAGE_FORMATS = ["image/jpeg", "image/jpg", "image/png"];

// 验证Base64图片
const validateImage = (base64Image: string) => {
  // 验证base64格式
  if (!base64Image.startsWith("data:image")) {
    throw new Error("无效的图片格式");
  }

  // 提取实际的base64数据和格式
  const [header, data] = base64Image.split(",");
  const format = header.split(";")[0].split(":")[1];

  // 验证图片格式
  if (!VALID_IMAGE_FORMATS.includes(format)) {
    throw new Error("不支持的图片格式，请使用JPG或PNG格式");
  }

  // 验证图片大小
  const binaryData = atob(data);
  if (binaryData.length > MAX_IMAGE_SIZE) {
    throw new Error(`图片大小不能超过${MAX_IMAGE_SIZE / 1024 / 1024}MB`);
  }

  // 返回处理后的base64数据（去掉header）
  return data;
};

const getAccessToken = async () => {
  const response = await axios.get(URL_GET_ACCESS_TOKEN, {
    params: {
      grant_type: "client_credentials",
      client_id: AK,
      client_secret: SK,
    },
  });
  return response.data.access_token;
};

export const useApiFace = () => {
  const addFace = async (userId: string, faceImage: string) => {
    try {
      // 验证并处理图片
      const processedImage = validateImage(faceImage);

      const response = await axios.post(
        URL_FACE_REGEDIT,
        {
          user_id: userId,
          image: processedImage,
          image_type: "BASE64",
          group_id: "g1",
          quality_control: "NORMAL", // 添加质量控制参数
          liveness_control: "LOW", // 添加活体检测参数
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
          params: {
            access_token: await getAccessToken(),
          },
        }
      );

      if (response.data.error_code !== 0) {
        throw new Error(response.data.error_msg || "人脸注册失败");
      }

      return response.data;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`人脸注册失败: ${error.message}`);
      }
      throw error;
    }
  };

  const searchFace = async (faceImage: string) => {
    try {
      // 验证并处理图片
      const processedImage = validateImage(faceImage);
      const response = await axios.post(
        URL_FACE_SEARCH,
        JSON.stringify({
          image: processedImage,
          image_type: "BASE64",
          group_id_list: "g1",
          quality_control: "NORMAL",
          liveness_control: "HIGH",
          match_threshold: 80,
        }),
        {
          headers: {
            "Content-Type": "application/json",
          },
          params: {
            access_token: await getAccessToken(),
          },
        }
      );
      return response.data;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`人脸搜索失败: ${error.message}`);
      }
      throw error;
    }
  };

  return {
    addFace,
    searchFace,
  };
};
