import type { <PERSON><PERSON><PERSON><PERSON>, EventHandlerRequest, H3Error } from "h3";
import { Prisma, PrismaClient } from "@prisma/client";

type ApiResponseData<T> = {
  /**
   * 成功=1
   * 错误=0
   */
  code: 0 | 1;
  /**
   * 返回消息
   */
  message: string;
  /**
   * 返回数据
   */
  data: T;
};
type TableData<T> = {
  total: number;
  result: Array<T> | [];
};
export const defineTableResponseHandler = <T extends EventHandlerRequest, D>(
  handler: EventHandler<T, Promise<TableData<D>>>
): EventHandler<T, ApiResponseData<TableData<D>>> =>
  defineEventHandler<T>(
    async (
      event
    ): Promise<
      | { code: number; message: string; data: TableData<D> }
      | { code: number; message: any; data: never[] }
    > => {
      try {
        const data = await handler(event);
        const response = {
          code: 1,
          message: "success",
          data,
        };
        return response;
      } catch (error: any) {
        console.log(error);
        switch (error.statusCode) {
          case 200:
            return {
              code: 0,
              message: error,
              data: [],
            };
          default:
            throw createError(error);
        }
      }
    }
  );

export const defineRowResponseHandler = <T extends EventHandlerRequest, D>(
  handler: EventHandler<T, Promise<D>>
): EventHandler<T, ApiResponseData<D>> =>
  defineEventHandler<T>(
    async (
      event
    ): Promise<
      | { code: number; message: string; data: Awaited<D> }
      | { code: number; message: any; data: {} }
    > => {
      try {
        const data = await handler(event);
        const response = {
          code: 1,
          message: "success",
          data,
        };
        return response;
      } catch (error: any) {
        switch (error.statusCode) {
          case 200:
            return {
              code: 0,
              message: error.message,
              data: {},
            };
          default:
            throw createError(error);
        }
      }
    }
  );
