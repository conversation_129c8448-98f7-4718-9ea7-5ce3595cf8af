import { H3Event } from "h3";
import { QueryValue } from "ufo";
export function useRequestParam(param?: QueryValue | QueryValue[]) {
  return {
    getNumber: () => {
      return param ? Number(param) : undefined;
    },
    getNumberSetDefault: (defaultValue: number = 0) => {
      return param ? Number(param) : defaultValue;
    },
    getString: () => {
      return param ? String(param) : undefined;
    },
    getStringSetDefault: (defaultValue: string = "") => {
      return param ? String(param) : defaultValue;
    },
    getArray: () => {
      return param ? (Array.isArray(param) ? param : [param]) : [];
    },
    getArraySetDefault: (defaultValue: string[] = []) => {
      return param ? (Array.isArray(param) ? param : [param]) : defaultValue;
    },
    getBoolean: () => {
      switch (param) {
        case "true":
          return true;
        case "false":
          return false;
        case true:
          return true;
        case false:
          return false;
        case "1":
          return true;
        case "0":
          return false;
        case 1:
          return true;
        case 0:
          return false;
        default:
          return undefined;
      }
    },
    getBooleanSetDefault: (defaultValue: boolean = false) => {
      return param ? Boolean(param) : defaultValue;
    },
    getDate: () => {
      return param ? new Date(param as string) : undefined;
    },
    getDateSetDefault: (defaultValue: Date = new Date()) => {
      return param ? new Date(param as string) : defaultValue;
    },
  };
}
export function useRequestQuery(event: H3Event, key: any = "_") {
  const config = useRuntimeConfig(event);
  const query = getQuery(event);
  const size = config.public.tableSize;
  function getTake() {
    return size;
  }
  function getPage() {
    return useRequestParam(query.p).getNumber() || 1;
  }
  function getSkip() {
    return (getPage() - 1) * getTake();
  }
  function getPrismaTakeAndSkip() {
    return {
      take: getTake(),
      skip: getSkip(),
    };
  }
  return {
    getTake,
    getPage,
    getSkip,
    getPrismaTakeAndSkip,
    ...useRequestParam(query[key]),
  };
}
