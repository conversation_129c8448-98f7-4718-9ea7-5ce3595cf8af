import { H3Event } from "h3";
type ApiResponseData<T> = {
  /**
   * 成功=1
   * 错误=0
   */
  code: 0 | 1;
  /**
   * 返回消息
   */
  message: string;
  /**
   * 返回数据
   */
  data: T;
};
type ApiResponseTableData<T> = ApiResponseData<{
  total: number;
  result: Array<T> | [];
}>;
function table<T>(res: ApiResponseTableData<T>) {
  return res;
}
function row<T>(res: ApiResponseData<T>) {
  return res;
}
function error<T>(message: string, data?: T) {
  return {
    code: 0,
    message,
    data: data || ({} as T),
  };
}
export function useResponse<T>() {
  return {
    table,
    row,
    error,
  };
}
export function useResponseCheckLogin<T>(event: H3Event) {
  const user = event.context.auth;
  const response = useResponse<T>();
  if (!user) {
    return {
      table: <T>(res: ApiResponseTableData<T>) => {
        return response.table({
          code: 0,
          message: "未登录",
          data: {
            total: 0,
            result: [],
          },
        });
      },
      row: <T>(res: ApiResponseData<T>) => {
        return response.row({
          code: 0,
          message: "未登录",
          data: {} as T,
        });
      },
    };
  } else {
    return {
      table: <T>(res: ApiResponseTableData<T>) => {
        return response.table(res);
      },
      row: <T>(res: ApiResponseData<T>) => {
        return response.row(res);
      },
    };
  }
}
