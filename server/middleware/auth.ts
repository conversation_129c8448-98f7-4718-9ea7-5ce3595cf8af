import { H3Event } from "h3";
declare module "h3" {
  interface H3EventContext {
    auth?: API.LoginReturnType["data"];
  }
}
function useCheckLogin(event: H3Event) {
  const token = getCookie(event, "jwt");
  if (token) {
    return useJwt().verify(token);
  } else {
    return undefined;
  }
}
export default defineEventHandler((event) => {
  const user = useCheckLogin(event);
  if (user) {
    event.context.auth = user;
  } else {
    event.context.auth = undefined;
  }
});
