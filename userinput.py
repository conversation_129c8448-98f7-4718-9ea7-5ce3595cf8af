import readline
user_input = input("prompt: ")
print(user_input)
# import tempfile
# import os
# import subprocess

# # 创建临时文件
# with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as tmpfile:
#     tmp_name = tmpfile.name

# # 打开默认文本编辑器
# subprocess.call(['open', '-t', tmp_name])

# # 等待用户完成编辑
# input("prompt: ")

# # 读取内容
# with open(tmp_name, 'r') as f:
#     user_input = f.read()

# # 删除临时文件
# os.unlink(tmp_name)

# # print("您输入的内容:")
# print(user_input)