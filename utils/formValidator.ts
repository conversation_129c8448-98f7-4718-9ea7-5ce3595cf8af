import type { Rule, Rules } from "async-validator";
import AsyncValidator from "async-validator";
function interopImportCJSDefault<T>(d: T): T {
  return d && (d as DefaultWrapper<T>).__esModule
    ? (d as DefaultWrapper<T>).default
    : d;
}
type DefaultWrapper<T> = T & { default: T; __esModule?: boolean };
// export const Schema = interopImportCJSDefault(AsyncValidator);
class FormRule {
  // constructor(public rule:string, public message:string){}
  //添加用户表单验证
  addUser: Rules = {
    code: [
      { required: true, message: "请输入编号" },
      { min: 5, max: 20, message: "编号长度在5到20个字符" },
    ],
    username: [
      { required: true, message: "请输入用户名" },
      { min: 2, max: 20, message: "用户名长度在2到20个字符" },
    ],
    name: [
      { required: true, message: "请输入姓名" },
      { min: 2, max: 20, message: "姓名长度在2到20个字符" },
    ],
    password: [{ required: true, message: "请输入密码" }],
    repassword: [{ required: true, message: "请输入确认密码" }],
    Role: [{ required: true, message: "请选择角色" }],
    email: [
      { required: true, message: "请输入邮箱" },
      {
        type: "email",
        message: "请输入正确的邮箱地址",
      },
    ],
    contactTel: [{ required: true, message: "请输入联系电话" }],
  };
  //更新用户表单验证
  updateUser: Rules = {
    code: [
      {
        required: true,
        message: "请输入编号",
      },
    ],
    id: [
      {
        required: true,
        message: "id",
      },
    ],
  };
  //添加物料清单表单验证
  addBom: Rules = {
    title: [{ required: true, message: "请输入物料清单名称" }],
    product_sku_id: [{ required: true, message: "请选择产品SKU" }],
    version: [{ required: true, message: "请输入版本号" }],
    materials: [{ required: true, message: "请选择物料清单项" }],
  };
  //添加仓库表单验证
  addWarehouse: Rules = {
    name: [{ required: true, message: "请输入仓库名称" }],
    address: [{ required: true, message: "请输入仓库地址" }],
    lock: [{ required: true, message: "请选择是否锁定" }],
  };
  //更新仓库表单验证
  updateWarehouse: Rules = {
    id: [{ required: true, message: "请输入仓库ID" }],
    name: [{ required: true, message: "请输入仓库名称" }],
    address: [{ required: true, message: "请输入仓库地址" }],
    lock: [{ required: true, message: "请选择是否锁定" }],
  };
  //添加物料
  addMateriel: Rules = {
    code: [
      { required: true, message: "请输入编号" },
      { min: 2, max: 20, message: "编号长度在2到20个字符" },
    ],
    name: [{ required: true, message: "请输入名称" }],
    model: [{ required: true, message: "请输入型号" }],
    unit: [{ required: true, message: "请输入单位" }],
    sepc: [{ required: true, message: "请输入规格" }],
  };
}
export const formRules = new FormRule();
