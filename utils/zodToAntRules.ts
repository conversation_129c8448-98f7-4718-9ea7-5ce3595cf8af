import { z } from "zod";
import type { Rule } from "ant-design-vue/es/form";

type ZodStringCheck = {
  kind: string;
  value?: any;
  message?: string;
};

type ZodNumberCheck = {
  kind: string;
  value: number;
  message?: string;
};

/**
 * 将 Zod 验证规则转换为 Ant Design Vue 的表单验证规则
 * @param schema Zod schema
 * @returns Ant Design Vue 表单验证规则
 */
export function zodToAntRules(schema: z.ZodType<any, any, any>): Rule[] {
  const rules: Rule[] = [];
  // 处理可选项
  if (schema instanceof z.ZodOptional) {
    // 获取可选项内部的类型定义
    const innerType = schema._def.innerType;
    // 递归处理内部类型的验证规则
    const innerRules = zodToAntRules(innerType);
    // 合并内部规则,但移除required规则
    rules.push(...innerRules.filter((rule) => !rule.required));
  } else {
    rules.push({
      required: true,
      message: "此字段为必填项",
    });
  }

  // 处理字符串类型规则
  if (schema instanceof z.ZodString) {
    const def = schema._def;
    const checks = def.checks || [];
    // 最小长度
    const minLengthCheck = checks.find((check) => check.kind === "min");
    if (minLengthCheck) {
      rules.push({
        min: minLengthCheck.value,
        message:
          minLengthCheck.message || `最少 ${minLengthCheck.value} 个字符`,
      });
    }

    // 最大长度
    const maxLengthCheck = checks.find((check) => check.kind === "max");
    if (maxLengthCheck) {
      rules.push({
        max: maxLengthCheck.value,
        message:
          maxLengthCheck.message || `最多 ${maxLengthCheck.value} 个字符`,
      });
    }

    // 邮箱验证
    if (checks.some((check) => check.kind === "email")) {
      rules.push({
        type: "email",
        message: "请输入有效的邮箱地址",
      });
    }

    // 正则表达式验证
    const regexCheck = checks.find((check) => check.kind === "regex") as
      | { regex: RegExp; message?: string }
      | undefined;
    if (regexCheck) {
      rules.push({
        pattern: regexCheck.regex,
        message: regexCheck.message || "格式不正确",
      });
    }
  }

  // 处理数字类型规则
  if (schema instanceof z.ZodNumber) {
    const checks = schema._def.checks || [];

    // 最小值
    const minCheck = checks.find((check) => check.kind === "min") as
      | ZodNumberCheck
      | undefined;
    if (minCheck) {
      rules.push({
        min: minCheck.value,
        type: "number",
        message: minCheck.message || `不能小于 ${minCheck.value}`,
      });
    }

    // 最大值
    const maxCheck = checks.find((check) => check.kind === "max") as
      | ZodNumberCheck
      | undefined;
    if (maxCheck) {
      rules.push({
        max: maxCheck.value,
        type: "number",
        message: maxCheck.message || `不能大于 ${maxCheck.value}`,
      });
    }

    // 整数验证
    if (checks.some((check) => check.kind === "int")) {
      rules.push({
        type: "integer",
        message: "请输入整数",
      });
    }
  }

  // 处理数组类型规则
  if (schema instanceof z.ZodArray) {
    const def = schema._def;

    // 最小长度
    if (def.minLength) {
      rules.push({
        type: "array",
        min: def.minLength.value,
        message: `至少选择 ${def.minLength.value} 项`,
      });
    }

    // 最大长度
    if (def.maxLength) {
      rules.push({
        type: "array",
        max: def.maxLength.value,
        message: `最多选择 ${def.maxLength.value} 项`,
      });
    }
  }

  // 处理自定义验证规则
  if ("refinement" in schema._def) {
    rules.push({
      validator: async (_rule: Rule, value: any) => {
        try {
          await schema.parseAsync(value);
          return Promise.resolve();
        } catch (error) {
          if (error instanceof z.ZodError) {
            return Promise.reject(error.errors[0].message);
          }
          return Promise.reject("验证失败");
        }
      },
    });
  }

  return rules;
}

/**
 * 将 Zod 对象 schema 转换为 Ant Design Vue 的表单验证规则对象
 * @param schema Zod 对象 schema
 * @returns 表单验证规则对象
 */
export function zodObjectToAntRules(schema: z.ZodObject<any, any, any>) {
  const rules: Record<string, Rule[]> = {};
  const shape = schema.shape;

  for (const [key, value] of Object.entries(shape)) {
    rules[key] = zodToAntRules(value as z.ZodType);
  }

  return rules;
}
