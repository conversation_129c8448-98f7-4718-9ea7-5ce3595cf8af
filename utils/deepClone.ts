export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(deepClone) as unknown as T;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }

  if (obj instanceof RegExp) {
    return new RegExp(obj) as unknown as T;
  }

  const clone = Object.create(Object.getPrototypeOf(obj));
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      clone[key] = deepClone(obj[key]);
    }
  }
  return clone as T;
}

// const a = {
//     arr: [],
//     obj: {
//         a: 1,
//         b: 2,
//     },
//     str: '123',
//     num: 123,
//     bool: true,
//     date: new Date(),
//     reg: /123/,
//     func: () => { },
//     und: undefined,
//     nul: null,
//     symbol: Symbol('123'),
//     bigint: BigInt(123),
// }
// const b = deepClone(a);
// console.log(a === b);
