import fs from "fs";
import path from "path";

interface RouteMenu {
  name: string;
  path: string;
  component: string;
  children?: RouteMenu[];
}

export class RouteScanner {
  // 扫描路由
  static async scanRoutes(pagesDir: string = "pages"): Promise<RouteMenu[]> {
    const baseDir = path.resolve(process.cwd(), pagesDir);
    return this.scanDirectory(baseDir, "/");
  }

  // 扫描目录
  private static async scanDirectory(
    dir: string,
    basePath: string
  ): Promise<RouteMenu[]> {
    const files = fs.readdirSync(dir);
    const routes: RouteMenu[] = [];

    for (const file of files) {
      const fullPath = path.join(dir, file);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // 处理目录
        const dirName = file;
        const children = await this.scanDirectory(
          fullPath,
          `${basePath}${dirName}/`
        );

        if (children.length) {
          routes.push({
            name: this.formatName(dirName),
            path: `${basePath}${dirName}`,
            component: "",
            children,
          });
        }
      } else if (this.isVueFile(file)) {
        // 处理 Vue 文件
        const name = this.getRouteName(file);
        const routePath = this.getRoutePath(basePath, file);

        routes.push({
          name: this.formatName(name),
          path: routePath,
          component: `${basePath}${file}`.replace(/^\//, ""),
        });
      }
    }

    return routes;
  }

  private static isVueFile(file: string): boolean {
    return file.endsWith(".vue");
  }

  private static getRouteName(file: string): string {
    return file.replace(/\.vue$/, "").replace(/^index$/, "home");
  }

  private static getRoutePath(basePath: string, file: string): string {
    const routePath = file.replace(/\.vue$/, "");
    return routePath === "index"
      ? basePath.replace(/\/$/, "") || "/"
      : `${basePath}${routePath}`;
  }

  private static formatName(name: string): string {
    return name
      .replace(/-/g, " ")
      .replace(/\b\w/g, (c) => c.toUpperCase())
      .replace(/\s+/g, "");
  }
}
