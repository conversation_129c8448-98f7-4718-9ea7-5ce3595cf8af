import { z } from "zod";
import { baseTableSchema } from "./basetable";

// 任务工单类型枚举
export const taskWorkOrderTypes = [
  "配料",
  "换模具", 
  "准备",
  "卸车",
  "发货",
  "粉料",
  "其他"
] as const;

// 任务工单状态枚举
export const taskWorkOrderStatuses = [
  "draft",
  "pending", 
  "in_progress",
  "completed",
  "cancelled"
] as const;

// 任务工单优先级枚举
export const taskWorkOrderPriorities = [
  "low",
  "normal",
  "high", 
  "urgent"
] as const;

// 任务工单查询schema
export const taskWorkOrderQuerySchema = baseTableSchema.extend({
  title: z.string().optional(),
  type: z.string().optional(),
  status: z.string().optional(),
  priority: z.string().optional(),
  quick: z.string().optional(),
});

// 任务工单创建schema
export const taskWorkOrderCreateSchema = z.object({
  title: z.string().min(1, "请输入标题"),
  type: z.enum(taskWorkOrderTypes, {
    errorMap: () => ({ message: "请选择有效的任务类型" })
  }),
  estimatedDuration: z.number().min(0.1, "预计时长至少为0.1小时"),
  description: z.string().optional(),
  priority: z.enum(taskWorkOrderPriorities).default("normal"),
  assignee_id: z.number().optional(),
  participants: z.array(z.number()).min(1, "请至少选择一名参与人员"),
});

// 任务工单更新schema
export const taskWorkOrderUpdateSchema = z.object({
  id: z.number(),
  title: z.string().min(1, "请输入标题").optional(),
  type: z.enum(taskWorkOrderTypes).optional(),
  estimatedDuration: z.number().min(0.1, "预计时长至少为0.1小时").optional(),
  description: z.string().optional(),
  status: z.enum(taskWorkOrderStatuses).optional(),
  priority: z.enum(taskWorkOrderPriorities).optional(),
  assignee_id: z.number().optional(),
  participants: z.array(z.number()).optional(),
  actualStartAt: z.string().datetime().optional(),
  actualEndAt: z.string().datetime().optional(),
  actualDuration: z.number().optional(),
});

// 任务工单状态更新schema
export const taskWorkOrderStatusUpdateSchema = z.object({
  id: z.number(),
  status: z.enum(taskWorkOrderStatuses),
  actualStartAt: z.string().datetime().optional(),
  actualEndAt: z.string().datetime().optional(),
  actualDuration: z.number().optional(),
});

// 任务工单类型定义
export interface TaskWorkOrder {
  id: number;
  title: string;
  type: typeof taskWorkOrderTypes[number];
  estimatedDuration: number;
  description?: string;
  status: typeof taskWorkOrderStatuses[number];
  priority: typeof taskWorkOrderPriorities[number];
  createuser_id: number;
  assignee_id?: number;
  actualStartAt?: Date;
  actualEndAt?: Date;
  actualDuration?: number;
  createdAt: Date;
  updatedAt: Date;
}

// 任务工单详情类型（包含关联数据）
export interface TaskWorkOrderDetail extends TaskWorkOrder {
  createUser: {
    id: number;
    name: string;
  };
  assignee?: {
    id: number;
    name: string;
  };
  participants: Array<{
    id: number;
    user: {
      id: number;
      name: string;
    };
    role: string;
  }>;
}
