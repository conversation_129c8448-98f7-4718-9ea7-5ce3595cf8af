import { z } from "zod";
import { MaterielSchema } from "./prisma_zod";
import { baseTableSchema } from "./basetable";
import Decimal from "decimal.js";

export const materielSchema = MaterielSchema;
export interface Materiel extends z.infer<typeof materielSchema> {}

export interface MaterielListItem {
  uid: string;
  id: number;
  code: string;
  name: string;
  specification: string;
  model: string;
  unit: string;
  quantity?: number;
  price?: number;
  note?: string;
  batch_no?: string;
  warehouse_id?: number;
}

export const materielQuerySchema = baseTableSchema.extend({
  id: z.number().optional(),
  code: z.string().optional(),
  name: z.string().optional(),
  model: z.string().optional(),
  quick: z.string().optional(),
});
export interface MaterielQueryInput
  extends z.infer<typeof materielQuerySchema> {}

export const materielCreateSchema = z.object({
  name: z.string(),
  model: z.string(),
  type: z.array(z.string()),
  code: z.string().optional(),
  unit: z.string(),
  specification: z.string(),
  description: z.string().optional(),
});
export interface MaterielCreateInput
  extends z.infer<typeof materielCreateSchema> {}

export const materielUpdateSchema = z.object({
  id: z.number(),
  code: z.string().optional(),
  name: z.string().optional(),
  model: z.string().optional(),
  specification: z.string().optional(),
  unit: z.string().optional(),
  typeArray: z.array(z.string()).optional(),
  category: z.string().optional(),
  attribute: z.string().optional(),
  type: z.string().optional(),
  description: z.string().optional(),
  useable: z.boolean().optional(),
});
export interface MaterielUpdateInput
  extends z.infer<typeof materielUpdateSchema> {}
