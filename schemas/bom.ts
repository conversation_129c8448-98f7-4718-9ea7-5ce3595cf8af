import { z } from "zod";
import Decimal from "decimal.js";

export const bomCreateSchema = z.object({
  materielId: z.number(),
  bom: z.array(
    z.object({
      materielId: z.number(),
      quantity: z.custom<Decimal>(),
      note: z.string().optional(),
    })
  ),
  subProduct: z.array(
    z.object({
      materielId: z.number(),
      quantity: z.custom<Decimal>(),
      note: z.string().optional(),
    })
  ),
});
export const bomUpdateSchema = bomCreateSchema;

export type BomCreateInput = z.infer<typeof bomCreateSchema>;
export type BomUpdateInput = z.infer<typeof bomUpdateSchema>;
