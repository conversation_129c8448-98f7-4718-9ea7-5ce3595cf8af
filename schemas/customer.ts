import { z } from "zod";
import { baseTableSchema } from "./basetable";

// 客户查询schema
export const customerQuerySchema = baseTableSchema.extend({
  id: z.number().optional(),
  name: z.string().optional(),
  comname: z.string().optional(),
  email: z.string().optional(),
  tel: z.string().optional(),
  mobile: z.string().optional(),
  address: z.string().optional(),
  lock: z.boolean().optional(),
});
export type CustomerQueryInput = z.infer<typeof customerQuerySchema>;

// 客户创建schema
export const customerCreateSchema = z.object({
  name: z.string().min(1, "联系人姓名不能为空"),
  comname: z.string().min(1, "公司名称不能为空"),
  email: z.string().email("请输入有效的邮箱地址").optional().or(z.literal("")),
  tel: z.string().optional().or(z.literal("")),
  mobile: z.string().min(1, "手机号码不能为空"),
  address: z.string().min(1, "地址不能为空"),
  fox: z.string().optional().or(z.literal("")),
});
export type CustomerCreateInput = z.infer<typeof customerCreateSchema>;

// 客户更新schema
export const customerUpdateSchema = z.object({
  id: z.number(),
  name: z.string().min(1, "联系人姓名不能为空").optional(),
  comname: z.string().min(1, "公司名称不能为空").optional(),
  email: z.string().email("请输入有效的邮箱地址").optional().or(z.literal("")),
  tel: z.string().optional().or(z.literal("")),
  mobile: z.string().min(1, "手机号码不能为空").optional(),
  address: z.string().min(1, "地址不能为空").optional(),
  fox: z.string().optional().or(z.literal("")),
  lock: z.boolean().optional(),
});
export type CustomerUpdateInput = z.infer<typeof customerUpdateSchema>;

// 客户收货信息创建schema
export const customerReceivingInfoCreateSchema = z.object({
  customer_id: z.number(),
  recName: z.string().min(1, "收货人姓名不能为空"),
  recAddress: z.string().min(1, "收货地址不能为空"),
  recTel: z.string().min(1, "收货人电话不能为空"),
});
export type CustomerReceivingInfoCreateInput = z.infer<typeof customerReceivingInfoCreateSchema>;

// 客户收货信息更新schema
export const customerReceivingInfoUpdateSchema = z.object({
  id: z.number(),
  recName: z.string().min(1, "收货人姓名不能为空").optional(),
  recAddress: z.string().min(1, "收货地址不能为空").optional(),
  recTel: z.string().min(1, "收货人电话不能为空").optional(),
});
export type CustomerReceivingInfoUpdateInput = z.infer<typeof customerReceivingInfoUpdateSchema>;

// 客户发票信息创建schema
export const customerInvoiceInfoCreateSchema = z.object({
  customer_id: z.number(),
  title: z.string().min(1, "发票抬头不能为空"),
  taxNumber: z.string().min(1, "税号不能为空"),
  bankName: z.string().optional().or(z.literal("")),
  bankAccount: z.string().optional().or(z.literal("")),
  address: z.string().optional().or(z.literal("")),
  tel: z.string().optional().or(z.literal("")),
  invoiceType: z.enum(["common", "special", "electronic"]).default("common"),
});
export type CustomerInvoiceInfoCreateInput = z.infer<typeof customerInvoiceInfoCreateSchema>;

// 客户发票信息更新schema
export const customerInvoiceInfoUpdateSchema = z.object({
  id: z.number(),
  title: z.string().min(1, "发票抬头不能为空").optional(),
  taxNumber: z.string().min(1, "税号不能为空").optional(),
  bankName: z.string().optional().or(z.literal("")),
  bankAccount: z.string().optional().or(z.literal("")),
  address: z.string().optional().or(z.literal("")),
  tel: z.string().optional().or(z.literal("")),
  invoiceType: z.enum(["common", "special", "electronic"]).optional(),
});
export type CustomerInvoiceInfoUpdateInput = z.infer<typeof customerInvoiceInfoUpdateSchema>;
