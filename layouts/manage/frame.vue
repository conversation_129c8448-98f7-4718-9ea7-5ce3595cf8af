<template>
  <a-layout :style="pageStyle">
    <a-layout-header :style="headerStyle">
      <a-flex justify="space-between" align="center">
        <div :style="logoStyle"></div>
        <slot name="header"> </slot>
      </a-flex>
    </a-layout-header>
    <a-layout :hasSider="true" style="margin-top: 64px">
      <a-layout-sider :style="siderStyle"
        ><slot name="menu"> </slot
      ></a-layout-sider>
      <a-layout>
        <a-layout-content :style="contentStyle">
          <slot></slot>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-layout>
</template>
<script lang="ts" setup>
  import type { CSSProperties } from "vue";
  const pageStyle: CSSProperties = {
    width: "100%",
    height: "100%",
  };
  const headerStyle: CSSProperties = {
    // textAlign: "center",
    color: "#fff",
    height: "64px",
    // paddingInline: 50,
    width: "100%",
    padding: "0 10px",
    lineHeight: "64px",
    position: "fixed",
    zIndex: 1,
    backgroundColor: "rgb(32, 125, 195)",
  };
  const logoStyle: CSSProperties = {
    float: "left",
    width: "168px",
    height: "28px",
    margin: "16px 0px 16px 0px",
    backgroundImage: "url(/img/logo.gif)",
  };

  const contentStyle: CSSProperties = {
    minHeight: "240px",
    lineHeight: "120px",
    backgroundColor: "#e9e9eb",
    padding: "10px 10px 10px 5px",
    height: "calc(100% - 64px)",
    overflow: "auto!important",
    marginLeft: "200px",
    // backgroundColor: "rgb(115, 163, 212)",
  };
  const siderStyle: CSSProperties = {
    lineHeight: "120px",
    backgroundColor: "#e9e9eb",
    padding: "10px 5px 10px 10px",
    overflow: "auto!important",
    height: "calc(100% - 64px)",
    position: "fixed",
    // backgroundColor: "#3ba0e9",
  };

  const footerStyle: CSSProperties = {
    textAlign: "center",
    color: "#fff",
    backgroundColor: "rgb(24, 93, 135)",
  };
</script>
<style scoped>
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  :hover::-webkit-scrollbar-thumb {
    border-radius: 8px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
    background-color: rgba(0, 0, 0, 0.2);

    /* background-color: rgb(32, 125, 195); */
  }
</style>
