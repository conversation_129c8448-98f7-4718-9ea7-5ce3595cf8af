<template>
  <div>
    <ClientOnly>
      <a-result
        :status="error.statusCode"
        :title="error.statusCode"
        :sub-title="error.message"
      >
        <template #extra>
          <a-button type="primary" @click="back">返回上一页</a-button>
          <a-button @click="() => (detail = !detail)">查看详情</a-button>
          <!-- <a-button type="primary" @click="handleError">返回首页</a-button> -->
        </template>
        <div v-html="error.stack" v-if="detail"></div>
      </a-result>
    </ClientOnly>

    <!-- <a-typography-title :level="2">{{
      props.error.statusCode
    }}</a-typography-title>
    <a-button @click="handleError">清除错误</a-button> -->
  </div>
</template>
<script setup lang="ts">
  import { h } from "vue";
  interface Error {
    url: string;
    statusCode: 403 | 404 | 500;
    statusMessage: string;
    message: string;
    description: string;
    data: any;
    stack: string;
  }
  const props = defineProps({
    error: {
      type: Object as PropType<Error>,
      required: true,
    },
  });
  const status = computed(() => {
    return props.error.statusCode.toString();
  });
  const detail = ref(false);
  // const handleError = () => clearError({ redirect: "/" });
  const back = () => {
    const router = useRouter();
    router.back();
  };
</script>
