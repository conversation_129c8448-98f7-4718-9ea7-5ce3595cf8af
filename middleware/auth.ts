/**
 * 权限管理
 */
export default defineNuxtRouteMiddleware(async (to, from) => {
  const { hasPermission } = usePermission();

  // 如果路由配置了权限要求
  if (to.meta.permission) {
    // if (!hasPermission(to.meta.permission)) {
    //   // 没有权限，重定向到 403 页面
    //   return navigateTo("/403");
    // }
  }
  // 在实际应用中，你可能不会将每个路由重定向到 `/`
  // 但是在重定向之前检查 `to.path` 是很重要的，否则可能会导致无限重定向循环
  // const passURL = ["/storemanage", "/storemanage/", "/storemanage/login"];
  // if (!passURL.includes(to.path)) {
  //   const user = useUser();
  //   const { $emitter } = useNuxtApp();
  //   if (import.meta.client) {
  //     //客户端判断是否登录,未登录则弹出登录框
  //     const { code, data } = await $fetch("/api/user/loginstatus");
  //     if (code && data) {
  //       user.value = data;
  //     } else {
  //       setTimeout(() => {
  //         $emitter.emit("changeLoginBoxStatus", true);
  //       });
  //     }
  //   } else {
  //     //服务端判断是否登录,未登录则跳转到登录页面,这样有助于防止用户在服务端直接访问受保护的路由
  //     const token = useCookie("jwt");
  //     if (!token.value) {
  //       return navigateTo("/storemanage");
  //     }
  //   }
  // } else {
  //   if (import.meta.client) {
  //     // const { code, data } = await $fetch("/api/user/loginstatus");
  //     // if (code && data) {
  //     //   if (data) {
  //     //     return navigateTo(data.role.entry);
  //     //   }
  //     // }
  //   }
  // }
});
