#!/usr/bin/env node

/**
 * 这个脚本用于修复Prisma引擎问题
 * 特别是针对debian-openssl-3.0.x环境
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import os from 'os';

console.log('🔧 开始修复Prisma引擎问题...');

try {
  // 获取系统信息
  const platform = process.platform;
  const arch = process.arch;
  const release = os.release();
  
  console.log(`📊 系统信息: ${platform}-${arch}, 发行版本: ${release}`);
  
  // 检查是否为Debian/Ubuntu系统
  const isDebianBased = platform === 'linux' && 
    (fs.existsSync('/etc/debian_version') || 
     execSync('cat /etc/os-release').toString().toLowerCase().includes('debian') ||
     execSync('cat /etc/os-release').toString().toLowerCase().includes('ubuntu'));
  
  if (isDebianBased) {
    console.log('🐧 检测到Debian/Ubuntu系统');
    
    // 检查OpenSSL版本
    const opensslVersion = execSync('openssl version').toString();
    console.log(`🔐 OpenSSL版本: ${opensslVersion}`);
    
    const isOpenSSL3 = opensslVersion.includes('OpenSSL 3');
    if (isOpenSSL3) {
      console.log('🔐 检测到OpenSSL 3.x');
      
      // 确保schema.prisma包含正确的binaryTargets
      const schemaPath = path.resolve(process.cwd(), 'prisma/schema.prisma');
      if (fs.existsSync(schemaPath)) {
        let schemaContent = fs.readFileSync(schemaPath, 'utf8');
        
        // 检查是否包含debian-openssl-3.0.x
        if (!schemaContent.includes('debian-openssl-3.0.x')) {
          console.log('⚠️ 添加debian-openssl-3.0.x到binaryTargets');
          
          // 查找binaryTargets行
          const binaryTargetsMatch = schemaContent.match(/binaryTargets\s*=\s*\[(.*?)\]/s);
          if (binaryTargetsMatch) {
            // 已有binaryTargets，添加debian-openssl-3.0.x
            const newBinaryTargets = binaryTargetsMatch[1].trim().endsWith(',')
              ? binaryTargetsMatch[1] + ' "debian-openssl-3.0.x"'
              : binaryTargetsMatch[1] + ', "debian-openssl-3.0.x"';
            
            schemaContent = schemaContent.replace(
              /binaryTargets\s*=\s*\[(.*?)\]/s,
              `binaryTargets = [${newBinaryTargets}]`
            );
          } else {
            // 没有binaryTargets，添加整行
            schemaContent = schemaContent.replace(
              /(provider\s*=\s*"prisma-client-js")/,
              '$1\n  binaryTargets = ["native", "debian-openssl-3.0.x"]'
            );
          }
          
          // 写回文件
          fs.writeFileSync(schemaPath, schemaContent);
          console.log('✅ 已更新schema.prisma文件');
        } else {
          console.log('✅ schema.prisma已包含debian-openssl-3.0.x');
        }
        
        // 重新生成Prisma客户端
        console.log('🔄 重新生成Prisma客户端...');
        execSync('npx prisma generate', { stdio: 'inherit' });
        
        console.log('✅ Prisma客户端重新生成成功!');
      } else {
        console.error('❌ 找不到schema.prisma文件');
      }
    } else {
      console.log('ℹ️ 不是OpenSSL 3.x，无需修复');
    }
  } else {
    console.log('ℹ️ 不是Debian/Ubuntu系统，无需修复');
  }
  
  // 检查生成的引擎文件
  const enginePath = path.resolve(process.cwd(), 'node_modules/.prisma/client');
  if (fs.existsSync(enginePath)) {
    const engineFiles = fs.readdirSync(enginePath).filter(file => file.includes('engine'));
    console.log('📦 当前的引擎文件:');
    engineFiles.forEach(file => console.log(`   - ${file}`));
  }
  
  console.log('🎉 修复完成!');
} catch (error) {
  console.error('❌ 修复过程中出错:', error);
  process.exit(1);
}
