#!/usr/bin/env node

/**
 * 这个脚本用于在不同环境中正确生成Prisma引擎
 * 它会检测当前环境并使用适当的配置生成Prisma客户端
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import os from 'os';

// 确保scripts目录存在
const scriptsDir = path.resolve(process.cwd(), 'scripts');
if (!fs.existsSync(scriptsDir)) {
  fs.mkdirSync(scriptsDir, { recursive: true });
}

console.log('🚀 开始生成Prisma客户端...');

try {
  // 获取系统信息
  const platform = process.platform;
  const arch = process.arch;
  const release = os.release();

  console.log(`📊 检测到系统: ${platform}-${arch}, 发行版本: ${release}`);

  // 检查schema.prisma文件中的binaryTargets配置
  const schemaPath = path.resolve(process.cwd(), 'prisma/schema.prisma');
  let schemaContent = fs.readFileSync(schemaPath, 'utf8');

  // 检查是否包含当前环境的binaryTargets
  const binaryTargetsMatch = schemaContent.match(/binaryTargets\s*=\s*\[(.*?)\]/s);
  if (binaryTargetsMatch) {
    const binaryTargets = binaryTargetsMatch[1];
    console.log(`📋 当前binaryTargets配置: ${binaryTargets}`);

    // 确保包含debian-openssl-3.0.x
    if (platform === 'linux' && !binaryTargets.includes('debian-openssl-3.0.x')) {
      console.log('⚠️ 添加debian-openssl-3.0.x到binaryTargets');
      const newBinaryTargets = binaryTargets.replace(/\]/, ', "debian-openssl-3.0.x"]');
      schemaContent = schemaContent.replace(/binaryTargets\s*=\s*\[(.*?)\]/s, `binaryTargets = [${newBinaryTargets}`);
      fs.writeFileSync(schemaPath, schemaContent);
    }
  }

  // 执行prisma generate命令
  console.log('⚙️ 执行prisma generate...');
  execSync('npx prisma generate', { stdio: 'inherit' });

  // 验证生成结果
  const enginePath = path.resolve(process.cwd(), 'node_modules/.prisma/client');
  if (fs.existsSync(enginePath)) {
    console.log('✅ Prisma客户端生成成功!');

    // 列出生成的引擎文件
    const engineFiles = fs.readdirSync(enginePath).filter(file => file.includes('engine'));
    console.log('📦 生成的引擎文件:');
    engineFiles.forEach(file => console.log(`   - ${file}`));
  } else {
    console.warn('⚠️ 无法找到生成的Prisma引擎文件');
  }
} catch (error) {
  console.error('❌ Prisma客户端生成失败:', error);
  console.error(error);
  process.exit(1);
}
