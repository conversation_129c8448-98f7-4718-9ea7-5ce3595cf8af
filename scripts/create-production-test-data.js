import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createProductionTestData() {
  try {
    console.log('开始创建生产测试数据...');

    // 1. 创建仓库（如果不存在）
    let warehouse = await prisma.warehouse.findFirst({
      where: { name: '主仓库' },
    });

    if (!warehouse) {
      warehouse = await prisma.warehouse.create({
        data: {
          name: '主仓库',
          address: '测试地址',
          type: 'production',
        },
      });
    }
    console.log('仓库创建完成:', warehouse.name);

    // 2. 获取或创建用户
    let user = await prisma.user.findFirst({
      where: { username: 'admin' },
    });

    if (!user) {
      user = await prisma.user.findFirst();
      if (!user) {
        console.log('未找到任何用户，请先创建用户');
        return;
      }
    }
    console.log('使用用户:', user.username);

    // 3. 创建物料
    const materials = [
      {
        name: '成品A',
        code: 'PROD001',
        specification: '成品规格A',
        unit: '个',
        category: '成品',
        attribute: '成品属性',
        type: '成品',
        model: 'A型',
        createuser_id: user.id,
        warehouse_id: warehouse.id,
      },
      {
        name: '原料B',
        code: 'RAW001',
        specification: '原料规格B',
        unit: '个',
        category: '原材料',
        attribute: '原料属性',
        type: '原料',
        model: 'B型',
        createuser_id: user.id,
        warehouse_id: warehouse.id,
      },
      {
        name: '原料C',
        code: 'RAW002',
        specification: '原料规格C',
        unit: '个',
        category: '原材料',
        attribute: '原料属性',
        type: '原料',
        model: 'C型',
        createuser_id: user.id,
        warehouse_id: warehouse.id,
      },
    ];

    const createdMaterials = [];
    for (const material of materials) {
      const mat = await prisma.materiel.upsert({
        where: { code: material.code },
        update: {},
        create: {
          ...material,
          stock: 100, // 初始库存
        },
      });
      createdMaterials.push(mat);
      console.log('物料创建完成:', mat.name);
    }

    // 4. 创建库存记录（只为原料创建库存）
    const rawMaterials = createdMaterials.filter(m => m.type === '原料');
    for (const material of rawMaterials) {
      await prisma.stock.upsert({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id: material.id,
            warehouse_id: warehouse.id,
            batch_no: `BATCH${material.code}001`,
          },
        },
        update: {},
        create: {
          materiel_id: material.id,
          warehouse_id: warehouse.id,
          batch_no: `BATCH${material.code}001`,
          quantity: 100,
          location: 'A-01-01',
        },
      });
      console.log('库存创建完成:', material.name, '数量: 100');
    }

    // 5. 创建BOM关系（成品A由原料B和原料C组成）
    const productA = createdMaterials.find(m => m.code === 'PROD001');
    const rawB = createdMaterials.find(m => m.code === 'RAW001');
    const rawC = createdMaterials.find(m => m.code === 'RAW002');

    if (productA && rawB && rawC) {
      // 删除已存在的BOM关系
      await prisma.materielBom.deleteMany({
        where: { parentId: productA.id },
      });

      // 创建新的BOM关系
      await prisma.materielBom.createMany({
        data: [
          {
            parentId: productA.id,
            childId: rawB.id,
            quantity: 2, // 生产1个成品A需要2个原料B
            remark: '主要原料',
          },
          {
            parentId: productA.id,
            childId: rawC.id,
            quantity: 3, // 生产1个成品A需要3个原料C
            remark: '辅助原料',
          },
        ],
      });
      console.log('BOM关系创建完成');
    }

    // 6. 创建生产工单
    const taskCode = `TASK${Date.now()}`;
    const productionTask = await prisma.productionTask.create({
      data: {
        code: taskCode,
        description: '测试生产工单',
        materiel_id: productA.id,
        quantity: 10, // 计划生产10个成品A
        startAt: new Date(),
        endAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后
        status: 1, // 待生产
      },
    });
    console.log('生产工单创建完成:', productionTask.code);

    console.log('生产测试数据创建完成！');
    console.log('=== 测试数据摘要 ===');
    console.log('成品:', productA.name, '(', productA.code, ')');
    console.log('原料B:', rawB.name, '(', rawB.code, ') - 需要量: 2个/成品');
    console.log('原料C:', rawC.name, '(', rawC.code, ') - 需要量: 3个/成品');
    console.log('生产工单:', productionTask.code, '- 计划数量: 10个');
    console.log('预计需要原料B:', 10 * 2, '个');
    console.log('预计需要原料C:', 10 * 3, '个');
    
  } catch (error) {
    console.error('创建生产测试数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createProductionTestData();
