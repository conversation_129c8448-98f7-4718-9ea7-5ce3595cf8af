import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestStockCheck() {
  try {
    console.log('开始创建测试盘点单...');

    // 获取仓库
    const warehouse = await prisma.warehouse.findFirst({
      where: { name: '主仓库' },
    });

    if (!warehouse) {
      console.log('未找到仓库，请先运行 create-test-data.js');
      return;
    }

    // 获取用户（假设admin用户ID为1）
    let user = await prisma.user.findFirst({
      where: { username: 'admin' },
    });

    if (!user) {
      // 尝试查找第一个用户
      user = await prisma.user.findFirst();
      if (!user) {
        console.log('未找到任何用户');
        return;
      }
      console.log('使用用户:', user.username);
    }

    // 生成盘点单号
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10).replace(/-/g, "");
    const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, "");
    const checkNo = `PD${dateStr}${timeStr}`;

    // 获取库存数据
    const stockItems = await prisma.stock.findMany({
      where: {
        warehouse_id: warehouse.id,
        quantity: { gt: 0 },
      },
      include: {
        materiel: true,
      },
    });

    if (stockItems.length === 0) {
      console.log('该仓库没有库存数据');
      return;
    }

    // 创建盘点单
    const stockCheck = await prisma.$transaction(async (tx) => {
      // 创建盘点单
      const check = await tx.stockCheck.create({
        data: {
          checkNo,
          title: '测试盘点单',
          warehouse_id: warehouse.id,
          checkType: 'full',
          plannedDate: new Date(),
          checkUser_id: user.id,
          note: '这是一个测试盘点单',
          totalItems: stockItems.length,
        },
      });

      // 创建盘点明细
      const checkItems = stockItems.map((stock) => ({
        stockCheck_id: check.id,
        materiel_id: stock.materiel_id,
        batch_no: stock.batch_no,
        location: stock.location,
        systemQuantity: stock.quantity,
      }));

      await tx.stockCheckItem.createMany({
        data: checkItems,
      });

      return check;
    });

    console.log('测试盘点单创建成功:', stockCheck.checkNo);
    console.log('盘点单ID:', stockCheck.id);
    console.log('盘点项数:', stockItems.length);

  } catch (error) {
    console.error('创建测试盘点单失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestStockCheck();
