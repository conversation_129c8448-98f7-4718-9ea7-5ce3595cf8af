import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestData() {
  try {
    console.log('开始创建测试数据...');

    // 1. 创建仓库（如果不存在）
    let warehouse = await prisma.warehouse.findFirst({
      where: { name: '主仓库' },
    });

    if (!warehouse) {
      warehouse = await prisma.warehouse.create({
        data: {
          name: '主仓库',
          address: '测试地址',
          type: 'production',
        },
      });
    }
    console.log('仓库创建完成:', warehouse.name);

    // 2. 创建物料（如果不存在）
    const materials = [
      {
        name: '测试物料A',
        code: 'MAT001',
        specification: '规格A',
        unit: '个',
        category: '原材料',
        attribute: '测试属性',
        type: '原料',
        model: 'A型',
        createuser_id: 1, // 假设admin用户ID为1
      },
      {
        name: '测试物料B',
        code: 'MAT002',
        specification: '规格B',
        unit: '个',
        category: '原材料',
        attribute: '测试属性',
        type: '原料',
        model: 'B型',
        createuser_id: 1,
      },
      {
        name: '测试物料C',
        code: 'MAT003',
        specification: '规格C',
        unit: '个',
        category: '原材料',
        attribute: '测试属性',
        type: '原料',
        model: 'C型',
        createuser_id: 1,
      },
    ];

    const createdMaterials = [];
    for (const material of materials) {
      const mat = await prisma.materiel.upsert({
        where: { code: material.code },
        update: {},
        create: {
          ...material,
          stock: 100, // 初始库存
        },
      });
      createdMaterials.push(mat);
      console.log('物料创建完成:', mat.name);
    }

    // 3. 创建库存记录
    for (const material of createdMaterials) {
      await prisma.stock.upsert({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id: material.id,
            warehouse_id: warehouse.id,
            batch_no: `BATCH${material.code}001`,
          },
        },
        update: {},
        create: {
          materiel_id: material.id,
          warehouse_id: warehouse.id,
          batch_no: `BATCH${material.code}001`,
          quantity: 100,
          location: 'A-01-01',
        },
      });
      console.log('库存创建完成:', material.name, '数量: 100');
    }

    console.log('测试数据创建完成！');
  } catch (error) {
    console.error('创建测试数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestData();
