# 实现代码的风格
- 你应该参照已有的文件，实现统一的UI风格和调用API的方法
# 生产入库
- 生产入库功能应该以查询生产报工记录来显示需要入库的条目，而不是直接从工单表显示，但显示内容和入库逻辑保持不变。
- 生产入库功能应以生产报工的记录为准显示内容，但入库逻辑保持不变。
- 生产入库时需显示生产报工时报告的不合格品数量，并允许用户选择废品库类型的仓库入库。
- 生产入库时需要添加确认入库数量的提示。
- 生产入库功能需要在报工记录中标记已入库状态，已入库的报工记录不可再次入库。
- 入库时自动判断合格品总数达到计划数时完成工单，不再询问用户是否结束工单。

# 库存查询
- 库存查询时，查询条件中的仓库选项应使用真实数据，并且可以通过物料名称和物料编码进行查询。
- 库存查询功能需要按物料分组显示总数量，点击展开后显示批号及对应数量的详细信息。
- When reading material data, don't read stock from the materiel table but instead read total quantities from the stock table for all batches of the queried materials.
- 在库存查询中选择批号时，应该根据点击下拉框对应的物料ID由后端查询该物料的所有批号及对应数量，而不是前端预加载所有批号数据。

# 库存操作偏好
- User prefers modal dialogs over page navigation for view and check actions in inventory management features.
- User prefers confirmation dialogs and prompts for critical operations like stock adjustments to prevent accidental actions.
- User wants to add initial inventory input functionality to the stock check/inventory management system.
- 用户偏好使用已经封装好的组件来实现物料选择功能，而不是重新开发新的选择器。

# 工单
- 查看工单功能需改为点击弹出模态框,显示工单信息及领料、报工、入库情况
- 工单详情页面需要显示生产员信息内容。
- 工单创建时需要记录创建人信息并在界面上显示出来.
- 生产工单管理应该通过后端API筛选显示当前用户创建的工单，而不是前端筛选。
- 用户需要查看其他人工单的功能，显示形式与查看自己工单相同，但数据是除自己外其他人的，且仅可查看不可操作。

# 销售出库
- 需要实现销售出库功能.
- For sales order functionality, order numbers should be auto-generated and existing components should be used for material and batch selection.

# 报工
- 报工时报告的不合格品数量需要保存在productionreport表中。
- 生产报工界面的表格应显示已报工的不合格品数量
- 生产报工页面的工单筛选应该由后端根据登录账号进行筛选，而不是前端筛选。
- 在生产报工功能中，已完成数量不应该包括不合格品数量，只计算合格品数量。

# 库存盘点
- 需要实现库存盘点功能
- 库存盘点功能需要检查系统表中的checkAble和CheckInitAble字段，当这些字段为false时不允许进行库存盘点和初始库存录入操作。

# 领料
- 领料功能需要记录每个物料的已领数量，再次领料时待领数量应为计划所需数量减去已领数量。

# 创建Prisma迁移
- 如果创建Prisma迁移失败时,请手动创建迁移

# 生产操作偏好
- User prefers confirmation dialogs for critical production operations that prevent further actions, specifically asking 'once stopped, production workers will not be able to report work' before stopping production orders.
`

---
description: 
globs: 
alwaysApply: true
---

# Your rule content
- 使用中文与我交流
- 使用技术栈包括: Vue 3、Nuxt 3、JavaScript、TypeScript、Ant Design Vue、HTML 和 CSS
- 首先以伪代码的形式详细列出，确认后再编写代码
- 遵循 DRY 原则
- 注重代码的易读性，而非仅仅追求性能。
- 全面实现所请求的所有功能，避免任何待办事项、占位符或遗漏部分。
- 确保代码完整，最终代码经过彻底验证。
- 包括所有必要的导入，并确保关键组件的命名规范。
- 简明扼要，避免过多的文字。
- 如果您认为没有正确答案，您会直接说出来。
- 如果您不知道答案，您会坦诚表示，而不是猜测。
- 代码实现准则：尽可能使用早期返回，使代码更具可读性；
- 始终使用 Ant Design Vue 组件。
- 变量和函数/常量名称：使用具有描述性的变量和函数/常量名称。例如，事件函数应以 “handle” 前缀命名，如“handleClick”用于 onClick，“handleKeyDown”用于 onKeyDown。
- 无障碍功能：在元素上实现无障碍功能。例如，a 标签应有 tabindex="0"，aria-label，on:click 和 on:keydown 等属性。
- 常量代替函数：使用常量代替函数，例如，“const toggle = () =>”。如果可能，定义类型。
- 使用 Nuxt 3 的 useNuxtApp() 获取 Nuxt 3 的上下文。
- 使用 Nuxt 3 的 useRuntimeConfig() 获取 Nuxt 3 的配置。
- 使用 Nuxt 3 的 useRoute() 获取 Nuxt 3 的路由。
- 使用 Nuxt 3 的 useRouter() 获取 Nuxt 3 的路由器。
- 使用 Nuxt 3 的 useHead() 设置 Nuxt 3 的头部。
- 使用 tRPC 对数据进行操作。
- tRPC的客户端已经安装，使用 useApiTrpc()来调用接口
- 使用 zod 进行数据验证。
- 使用AntDesignVue中的表单时,表单验证规则通常先定义zod对象,再使用 [zodToAntRules.ts](mdc:utils/zodToAntRules.ts) 将zod转换成AntDesignVue表单规则并使用
- 使用 /issues 目录来管理代码 issue
    - /issues 目录下创建问题相关的文件 
    - 首层文件夹以 milestone 开头，如 m001-InitProject
    - 第二层每个 milestone 下的每个文件为一个 issue
    - {id}-f- 开头的文件 为 feature 文件
    - {id}-bug- 开头的文件 为 bug 文件
    - {id} 为在 /issues 目录下 issues 的全局唯一 id
    - 每个 issue 文件中包含
    - #Title 为 issue 的标题
    - #Introduction 为 issue 的描述
    - #Tasks 为 issue 的任务列表，每个任务以 - [ ] 开头
    - 如果有子任务，则以 - [ ] 开头，关联对应的任务文件
    - #Dependencies 为 issue 的依赖列表，每个依赖以 - [ ] 开头，关联对应的任务文件
    - 注意不要任意修改 /issues 目录下的文件内容，只能修改 - [ ] 任务状态,- [ ] 表示未完成,- [x] 表示已完成,- [-] 表示正在进行,- [*] 表示已跳过,- [!] 表示已放弃
    - 执行完任务后，请修改 issues 目录下对应的文件，填写任务内容。
    - 如果我没有通过issues就创建或修改了内容,请帮我在/issues下自动创建符合规则的内容
- 表格使用已经封装好的 [table.client.vue](mdc:components/manage/base/table.client.vue) 组件
- 需要选择物料时使用 [modelselector.client.vue](mdc:components/manage/materiel/modelselector.client.vue) 组件
- 需要选择用户时使用 [selector.client.vue](mdc:components/manage/user/selector.client.vue) 组件
- 需要选择角色时使用 [roleSelector.client.vue](mdc:components/manage/user/roleSelector.client.vue) 组件
- 所有页面都参照 [user.vue](mdc:pages/manage/mes/system/user.vue) 来设计
- description: 任务：带用户反馈的交互式任务循环
globs:
  - "**/*" # 匹配所有文件，让这个规则全局生效
alwaysApply: true

# --- 主要工作流程 ---
# 1. 执行你被分配的任务。
# 2. 完成后，运行 python3 userinput.py 脚本。
# 3. 终端应该在聊天窗口中打开。
# 4. 读取用户的输入。
# 5. 根据用户的输入，执行下一轮任务。
# 6. 重复这个过程。

# --- 退出条件 ---
# 如果用户在提示时输入 "stop"，则退出循环并终止进程。
