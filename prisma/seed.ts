import { PrismaClient, Prisma } from "@prisma/client";
import md5 from "md5";

const prisma = new PrismaClient();

// const roleData: Prisma.RoleCreateInput[] = [
//   {
//     code: "suadmin",
//     name: "超级管理员",
//     entry: "/storemanage/admin",
//   },
//   {
//     code: "selleradmin",
//     name: "销售管理员",
//     entry: "/storemanage/admin",
//   },
//   {
//     code: "warehouseadmin",
//     name: "仓库管理员",
//     entry: "/storemanage/admin",
//   },
//   {
//     code: "purchaseadmin",
//     name: "采购管理员",
//     entry: "/storemanage/admin",
//   },
//   {
//     code: "financeadmin",
//     name: "财务管理员",
//     entry: "/storemanage/admin",
//   },
//   {
//     code: "productionadmin",
//     name: "生产管理员",
//     entry: "/storemanage/production",
//   },
//   {
//     code: "productionuser",
//     name: "生产员",
//     entry: "/storemanage/production",
//   },
// ];

const userData: Prisma.UserCreateInput[] = [
  {
    username: "uargly_admin",
    password: md5("Uarlabadmin_240930"),
    code: "uarlab001",
    name: "超级管理员",
    email: "<EMAIL>",
    Role: "SuperAdmin",
  },
  {
    username: "uarxs_admin",
    password: md5("Uarlabadmin_241026"),
    code: "uarlab002",
    name: "销售管理员",
    email: "<EMAIL>",
    Role: "SellerAdmin",
  },
  {
    username: "uarck_admin",
    password: md5("Uarlabadmin_241026"),
    code: "uarlab003",
    name: "仓库管理员",
    email: "<EMAIL>",
    Role: "WarehouseAdmin",
  },
  {
    username: "uarcg_admin",
    password: md5("Uarlabuser_241026"),
    code: "uarlab004",
    name: "采购管理员",
    email: "<EMAIL>",
    Role: "PurchaseAdmin",
  },
  {
    username: "uarcw_admin",
    password: md5("Uarlabuser_241026"),
    code: "uarlab005",
    name: "财务管理员",
    email: "<EMAIL>",
    Role: "FinanceAdmin",
  },
  {
    username: "uarsc_admin",
    password: md5("123456"),
    code: "uarlab006",
    name: "生产管理员",
    email: "<EMAIL>",
    Role: "ProductionAdmin",
  },
  {
    username: "uarscy",
    password: md5("123456"),
    name: "生产员",
    email: "<EMAIL>",
    Role: "ProductionUser",
  },
];

const productTypeData: Prisma.ProductTypeCreateInput[] = [
  {
    code: "01",
    name: "生化试剂",
    NextType: {
      create: [
        {
          code: "0100",
          name: "培养基",
          NextType: {
            create: [
              {
                code: "010001",
                name: "美国BD培养基",
              },
              {
                code: "010002",
                name: "牛牛基因培养基",
              },
              {
                code: "010003",
                name: "美国Nalgene培养基",
              },
              {
                code: "010004",
                name: "英国OXOID培养基",
              },
              {
                code: "010005",
                name: "默克培养基",
              },
              {
                code: "010006",
                name: "中检所对照培养基",
              },
            ],
          },
        },
        {
          code: "0104",
          name: "诊断血清",
          NextType: {
            create: {
              code: "010401",
              name: "诊断血清TR",
            },
          },
        },
        {
          code: "0105",
          name: "分子生物学",
          NextType: {
            create: [
              {
                code: "010501",
                name: "琼脂糖",
              },
              {
                code: "010502",
                name: "循环肿瘤细胞试剂盒",
              },
              {
                code: "010503",
                name: "分子生物学试剂",
              },
              {
                code: "010504",
                name: "抗体",
              },
            ],
          },
        },
        {
          code: "0106",
          name: "菌种",
        },
      ],
    },
  },
  {
    code: "02",
    name: "有机试剂",
  },
  {
    code: "03",
    name: "实验室耗材",
    NextType: {
      create: [
        {
          code: "0301",
          name: "药品检验",
          NextType: {
            create: [
              {
                code: "030101",
                name: "无菌检查",
              },
              {
                code: "030102",
                name: "微生物限度（一次性薄膜过滤器）",
              },
              {
                code: "030103",
                name: "微生物限度（重复使用过滤器）",
              },
              {
                code: "030104",
                name: "微生物限度（配套耗材）",
              },
              {
                code: "030105",
                name: "环境监测",
              },
            ],
          },
        },
        {
          code: "0302",
          name: "通用耗材",
          NextType: {
            create: [
              {
                code: "030201",
                name: "一次性耗材",
              },
              {
                code: "030202",
                name: "耐用耗材",
              },
              {
                code: "030203",
                name: "指示剂",
              },
              {
                code: "030204",
                name: "磁珠",
              },
              {
                code: "030205",
                name: "微孔板",
              },
              {
                code: "030206",
                name: "AS ONE产品",
              },
            ],
          },
        },
        {
          code: "0303",
          name: "酶联免疫试剂盒",
          NextType: {
            create: {
              code: "030301",
              name: "塑化剂检测试剂盒",
            },
          },
        },
      ],
    },
  },
  {
    code: "04",
    name: "实验室用品",
  },
  {
    code: "06",
    name: "分析试剂",
    NextType: {
      create: [
        {
          code: "0601",
          name: "药品检验标准品",
          NextType: {
            create: [
              {
                code: "060101",
                name: "USP标准品",
              },
              {
                code: "060102",
                name: "中检所对照品",
              },
              {
                code: "060103",
                name: "上海计量院标准品",
              },
              {
                code: "060104",
                name: "土壤对照品",
              },
            ],
          },
        },
        {
          code: "0602",
          name: "有机标准品",
          NextType: {
            create: {
              code: "060201",
              name: "质谱分析用同位素标准品",
            },
          },
        },
      ],
    },
  },
  {
    code: "07",
    name: "专用试剂",
    NextType: {
      create: [
        {
          code: "0700",
          name: "医疗器材",
          NextType: {
            create: {
              code: "070001",
              name: "模拟组织液",
            },
          },
        },
        {
          code: "0701",
          name: "药品检验",
          NextType: {
            create: [
              {
                code: "070100",
                name: "十四烷酸",
              },
              {
                code: "070101",
                name: "大环内酯类检测",
              },
              {
                code: "070102",
                name: "模拟胃肠液",
              },
            ],
          },
        },
      ],
    },
  },
  {
    code: "08",
    name: "科学仪器",
    NextType: {
      create: {
        code: "0802",
        name: "实验室小型设备",
        NextType: {
          create: { code: "080201", name: "药品检验设备" },
        },
      },
    },
  },
  {
    code: "09",
    name: "无机试剂",
  },
  {
    code: "10",
    name: "化学试剂",
  },
];

const invoiceTypeData: Prisma.InvoiceTypeCreateInput[] = [
  {
    typename: "不开发票",
  },
  {
    typename: "普通发票",
  },
  {
    typename: "增值税发票",
  },
  {
    typename: "数电普票",
  },
  {
    typename: "数电专票",
  },
];

export const seed = async () => {
  const state = [];
  state.push(`Start seeding ...`);

  // for (const role of roleData) {
  //   const r = await prisma.role.create({
  //     data: role,
  //   });
  //   state.push(`Created role with id: ${r.id}`);
  // }

  for (const u of userData) {
    const user = await prisma.user.create({
      data: u,
    });
    state.push(`Created user with id: ${user.id}`);
  }

  for (const pt of productTypeData) {
    const productType = await prisma.productType.create({
      data: pt,
    });
    state.push(`Created productType with id: ${productType.id}`);
  }

  const invoicetype = await prisma.invoiceType.createMany({
    data: invoiceTypeData,
  });
  state.push(`Created invoiceType count: ${invoicetype.count}`);

  const system = await prisma.system.create({
    data: {
      installed: true,
    },
  });
  state.push(`Seeding finished.`);
  return state;
};

const warehouseData: Prisma.WarehouseCreateInput[] = [
  {
    name: "测试仓库",
    address: "-",
    WarehouseOnUser: {
      create: {
        user: {
          connect: {
            id: 1,
          },
        },
      },
    },
  },
];
const productExampleData: Prisma.ProductCreateInput[] = [
  {
    title: "测试产品",
    code: "P0001",
    model: "测试型号",
    unit: "个",
    lock: false,
    productTypeId: {
      connect: {
        code: "010001",
      },
    },
    Admin: {
      connect: {
        id: 1,
      },
    },
  },
  {
    title: "测试产品2",
    code: "P0002",
    model: "测试型号2",
    unit: "个",
    lock: false,
    productTypeId: {
      connect: {
        code: "010002",
      },
    },
    Admin: {
      connect: {
        id: 1,
      },
    },
  },
];
const materielCategoryExampleData: Prisma.MaterielCategoryCreateInput[] = [
  {
    code: "01",
    name: "成品",
    NextType: {
      create: [
        {
          code: "0101",
          name: "电器",
          NextType: {
            create: [
              {
                code: "010101",
                name: "冰箱",
              },
              {
                code: "010102",
                name: "洗衣机",
              },
              {
                code: "010103",
                name: "手机",
              },
            ],
          },
        },
      ],
    },
  },
  {
    code: "02",
    name: "原料",
    NextType: {
      create: [
        {
          code: "0201",
          name: "手机原料",
          NextType: {
            create: [
              {
                code: "020101",
                name: "手机原料-1",
              },
              {
                code: "020102",
                name: "手机原料-2",
              },
            ],
          },
        },
        {
          code: "0202",
          name: "原料-2",
        },
      ],
    },
  },
  {
    code: "03",
    name: "半成品",
    NextType: {
      create: [
        {
          code: "0301",
          name: "手机半成品",
          NextType: {
            create: [
              {
                code: "030101",
                name: "手机半成品-1",
              },
              {
                code: "030102",
                name: "手机半成品-2",
              },
            ],
          },
        },
        {
          code: "0302",
          name: "半成品-2",
        },
      ],
    },
  },
];
const materielExampleData: Prisma.MaterielCreateInput[] = [
  {
    code: "M0001",
    name: "MyPhone手机",
    model: "A1006",
    description: "测试用商品",
    sepc: "台",
    unit: "台",
    MaterielType: {
      connect: {
        code: "010103",
      },
    },
    Admin: {
      connect: {
        id: 1,
      },
    },
  },
];

export const exampleSeed = async () => {
  const state = await seed();
  state.push(`Start example seeding ...`);
  for (const wh of warehouseData) {
    const warehouse = await prisma.warehouse.create({
      data: wh,
    });
    state.push(`Created warehouse with id: ${warehouse.id}`);
  }

  for (const p of productExampleData) {
    const product = await prisma.product.create({
      data: p,
    });
    state.push(`Created product with id: ${product.id}`);
  }

  for (const mc of materielCategoryExampleData) {
    const materielCategory = await prisma.materielCategory.create({
      data: mc,
    });
    state.push(`Created materielCategory with id: ${materielCategory.id}`);
  }

  for (const m of materielExampleData) {
    const materiel = await prisma.materiel.create({
      data: m,
    });
    state.push(`Created materiel with id: ${materiel.id}`);
  }

  state.push(`Seeding example finished.`);
  return state;
};

// const userData: Prisma.UserCreateInput[] = [
//   {
//     name: 'Alice',
//     email: '<EMAIL>',
//     posts: {
//       create: [
//         {
//           title: 'Join the Prisma Discord',
//           content: 'https://pris.ly/discord',
//           published: true,
//         },
//       ],
//     },
//   },
//   {
//     name: 'Nilu',
//     email: '<EMAIL>',
//     posts: {
//       create: [
//         {
//           title: 'Follow Prisma on Twitter',
//           content: 'https://www.twitter.com/prisma',
//           published: true,
//         },
//       ],
//     },
//   },
//   {
//     name: 'Mahmoud',
//     email: '<EMAIL>',
//     posts: {
//       create: [
//         {
//           title: 'Ask a question about Prisma on GitHub',
//           content: 'https://www.github.com/prisma/prisma/discussions',
//           published: true,
//         },
//         {
//           title: 'Prisma on YouTube',
//           content: 'https://pris.ly/youtube',
//         },
//       ],
//     },
//   },
// ]

// async function main() {
//   console.log(`Start seeding ...`)
//   for (const u of userData) {
//     const user = await prisma.user.create({
//       data: u,
//     })
//     console.log(`Created user with id: ${user.id}`)
//   }
//   console.log(`Seeding finished.`)
// }

// main()
//   .then(async () => {
//     await prisma.$disconnect();
//   })
//   .catch(async (e) => {
//     console.error(e);
//     await prisma.$disconnect();
//     process.exit(1);
//   });
