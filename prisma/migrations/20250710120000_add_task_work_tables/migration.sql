-- CreateTable
CREATE TABLE `TaskWorkOrder` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(255) NOT NULL,
    `type` VARCHAR(100) NOT NULL,
    `estimatedDuration` DECIMAL(10, 2) NOT NULL,
    `description` TEXT NULL,
    `status` VARCHAR(50) NOT NULL DEFAULT 'draft',
    `priority` VARCHAR(50) NOT NULL DEFAULT 'normal',
    `createuser_id` INTEGER NOT NULL,
    `assignee_id` INTEGER NULL,
    `actualStartAt` DATETIME(3) NULL,
    `actualEndAt` DATETIME(3) NULL,
    `actualDuration` DECIMAL(10, 2) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `TaskWorkOrderUsers` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `task_id` INTEGER NOT NULL,
    `user_id` INTEGER NOT NULL,
    `role` VARCHAR(50) NOT NULL DEFAULT 'participant',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `TaskWorkOrderUsers_task_id_user_id_key`(`task_id`, `user_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `TaskWorkOrder` ADD CONSTRAINT `TaskWorkOrder_createuser_id_fkey` FOREIGN KEY (`createuser_id`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `TaskWorkOrder` ADD CONSTRAINT `TaskWorkOrder_assignee_id_fkey` FOREIGN KEY (`assignee_id`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `TaskWorkOrderUsers` ADD CONSTRAINT `TaskWorkOrderUsers_task_id_fkey` FOREIGN KEY (`task_id`) REFERENCES `TaskWorkOrder`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `TaskWorkOrderUsers` ADD CONSTRAINT `TaskWorkOrderUsers_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;