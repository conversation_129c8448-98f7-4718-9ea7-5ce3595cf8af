export const useAuth = () => {
  const { $client } = useNuxtApp();
  const { setPermissions } = usePermission();
  const login = async (credentials: LoginCredentials) => {
    try {
      // 登录获取 token
      const user = await useApiTrpc().public.auth.login.mutate(credentials);
      // TODO: 获取用户权限
      //   const userPermissions = await $client.auth.getPermissions.query();
      useState("user", () => user.data);
      // setPermissions({
      //   permissions: ["home", "user", "user/user", "user/customer"],
      //   roles: ["admin", "user"],
      // });
      return user;
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    }
  };
  const getUser = async () => {
    const user = await useApiTrpc().public.auth.status.query();
    useState("user", () => user.data);
    return user.data;
  };
  const getMenu = async () => {
    const menu = await useApiTrpc().public.auth.menu.query();
    return menu.data;
  };
  // useState("user", async () => {
  //   // return $client.query("public.login.loginstatus");
  //   const user = await useApiTprc().public.auth.status.query();
  //   return user.data;
  // }).value;

  return {
    login,
    getUser,
    getMenu,
  };
};

type LoginCredentials = {
  username: string;
  password: string;
  verifycode: string;
};
