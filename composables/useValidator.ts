import type { Rule, Rules } from "async-validator";
import AsyncValida<PERSON> from "async-validator";

type DefaultWrapper<T> = T & { default: T; __esModule?: boolean };
export const useValidator = (rules: Rules) => {
  function interopImportCJSDefault<T>(d: T): T {
    return d && (d as DefaultWrapper<T>).__esModule
      ? (d as DefaultWrapper<T>).default
      : d;
  }
  const Schema = interopImportCJSDefault(AsyncValidator);
  return new Schema(rules);
};
