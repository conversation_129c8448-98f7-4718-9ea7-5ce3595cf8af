<template>
  <manage-base-modalform
    v-model:visible="visible"
    v-model:loading="loading"
    title="编辑客户"
    :model="formState"
    @submit="handleSubmit"
  >
    <template #default="{ show }">
      <slot :show="show"></slot>
    </template>
    <template #form>
      <a-form-item
        label="联系人姓名"
        name="name"
        :rules="[{ required: true, message: '请输入联系人姓名' }]"
      >
        <a-input v-model:value="formState.name" placeholder="请输入联系人姓名" />
      </a-form-item>
      <a-form-item
        label="公司名称"
        name="comname"
        :rules="[{ required: true, message: '请输入公司名称' }]"
      >
        <a-input v-model:value="formState.comname" placeholder="请输入公司名称" />
      </a-form-item>
      <a-form-item
        label="手机号码"
        name="mobile"
        :rules="[{ required: true, message: '请输入手机号码' }]"
      >
        <a-input v-model:value="formState.mobile" placeholder="请输入手机号码" />
      </a-form-item>
      <a-form-item label="电话" name="tel">
        <a-input v-model:value="formState.tel" placeholder="请输入电话" />
      </a-form-item>
      <a-form-item
        label="邮箱"
        name="email"
        :rules="[
          { type: 'email', message: '请输入有效的邮箱地址' },
        ]"
      >
        <a-input v-model:value="formState.email" placeholder="请输入邮箱" />
      </a-form-item>
      <a-form-item
        label="地址"
        name="address"
        :rules="[{ required: true, message: '请输入地址' }]"
      >
        <a-textarea
          v-model:value="formState.address"
          placeholder="请输入地址"
          :rows="3"
        />
      </a-form-item>
      <a-form-item label="传真" name="fox">
        <a-input v-model:value="formState.fox" placeholder="请输入传真" />
      </a-form-item>
    </template>
  </manage-base-modalform>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue';
import type { CustomerUpdateInput } from '~/schemas/customer';

// 表单状态
const formState = reactive<CustomerUpdateInput>({
  id: 0,
  name: '',
  comname: '',
  mobile: '',
  tel: '',
  email: '',
  address: '',
  fox: '',
});

// 表单状态
const visible = ref(false);
const loading = ref(false);

// TRPC接口引用
const trpc = useApiTrpc();
const updateCustomerMutation = trpc.admin.customer.updateCustomer.mutate;

// 提交表单
const handleSubmit = async () => {
  try {
    await updateCustomerMutation(formState);
    message.success('更新客户成功');
    visible.value = false;
    loading.value = false;
    emits('saveSuccess');
  } catch (error) {
    loading.value = false;
    message.error('更新客户失败');
    console.error(error);
  }
};

// 显示表单
const show = (record: CustomerUpdateInput) => {
  Object.assign(formState, record);
  visible.value = true;
};

// 定义事件
const emits = defineEmits<{
  (e: 'saveSuccess'): void;
}>();

// 暴露方法
defineExpose({
  show,
});
</script>
