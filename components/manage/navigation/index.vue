<template>
  <div>
    <a-menu
      id="leftmenu"
      v-model:selectedKeys="selectedKeys"
      mode="inline"
      @click="handleClick"
      style="border-radius: 8px"
      :disabledOverflow="true"
    >
      <component v-for="item in items" :is="getItem(item)"></component>
    </a-menu>
  </div>
</template>
<script setup lang="ts">
  import { h, type Slot } from "vue";
  import { Menu } from "ant-design-vue";

  import type { MenuProps, ItemType } from "ant-design-vue";
  import type { MenuInfo } from "ant-design-vue/es/menu/src/interface";
  export type MenuItem = {
    id?: number;
    title: string;
    name: string;
    icon?: Slot;
    disabled?: boolean;
    type: "item" | "group" | "sub";
    sort?: number;
    items?: MenuItem[];
    visible?: boolean;
  };

  const props = defineProps({
    items: {
      type: Array as PropType<MenuItem[]>,
      default: (): MenuItem[] => [],
    },
  });
  const getItem = (item: MenuItem) => {
    if (item.type === "item" && item.visible) {
      return h(
        Menu.Item,
        {
          key: item.name,
          label: item.title,
          title: item.title,
          icon: item.icon,
          disabled: item.disabled,
        },
        { default: () => item.title }
      );
    }
    if (item.type === "sub" && item.visible) {
      return h(
        Menu.SubMenu,
        { key: item.name, title: item.title },
        {
          default: () => {
            if (item.items && item.items.length > 0) {
              return item.items.map((item) => getItem(item));
            }
          },
        }
      );
    }
    if (item.type === "group" && item.visible) {
      return h(
        Menu.ItemGroup,
        { key: item.name, title: item.title },
        {
          default: () => {
            if (item.items && item.items.length > 0) {
              return item.items.map((item) => getItem(item));
            }
          },
        }
      );
    }
  };
  const selectedKeys = defineModel<string[]>("selectedKeys", {
    default: () => ["home"],
  });
  const emits = defineEmits<{
    selectMenuItem: [MenuInfo];
  }>();
  const handleClick: MenuProps["onClick"] = (e) => {
    selectedKeys.value = [e.key as string];
    emits("selectMenuItem", e);
    console.log(e);
  };
</script>
