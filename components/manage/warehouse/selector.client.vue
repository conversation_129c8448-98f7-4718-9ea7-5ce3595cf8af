<template>
  <a-select
    v-model:value="value"
    show-search
    placeholder="请选择仓库"
    :options="options"
    :filter-option="filterOption"
    @focus="handleFocus"
    @blur="handleBlur"
    @change="handleChange"
    :loading="loading"
  ></a-select>
</template>
<script lang="ts" setup>
  import type { SelectProps } from "ant-design-vue";
  import { WarehouseType } from "@prisma/client";
  import { ref } from "vue";

  const options = ref<SelectProps["options"]>([]);
  const loading = ref(false);

  type WarehouseTypeArray = WarehouseType[];
  const props = defineProps({
    type: {
      type: Array as PropType<WarehouseTypeArray>,
      default: ["production"],
    },
  });

  onMounted(async () => {
    await fetchWarehouseList();
  });

  // 获取仓库列表
  const fetchWarehouseList = async () => {
    loading.value = true;
    try {
      const queryWarehouseList =
        useApiTrpc().admin.warehouse.queryWarehouseList.query;
      const response = await queryWarehouseList({
        take: 100, // 获取最多100个仓库
        skip: 0,
        type: props.type,
      });

      if (response.data && response.data.result) {
        options.value = response.data.result.map((item: any) => {
          return {
            value: item.id,
            label: item.name,
            disabled: item.lock, // 如果仓库被锁定，则禁用选项
          };
        });
      }
    } catch (error) {
      console.error("获取仓库列表失败", error);
    } finally {
      loading.value = false;
    }
  };

  // 处理选中变化
  const handleChange = (selectedValue: any) => {
    console.log(`选中仓库: ${selectedValue}`);
  };

  // 处理失去焦点
  const handleBlur = () => {
    console.log("失去焦点");
  };

  // 处理获得焦点
  const handleFocus = async () => {
    console.log("获得焦点");
    // 可以在这里重新获取仓库列表，以保证数据最新
    await fetchWarehouseList();
  };

  // 搜索过滤
  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  // 使用defineModel定义双向绑定的值
  const value = defineModel<number | undefined>("value", {
    required: true,
  });

  // 暴露方法给父组件
  defineExpose({
    refresh: fetchWarehouseList,
  });
</script>
