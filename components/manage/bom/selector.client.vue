<template>
  <div>
    <a-select v-model:value="value" :options="options" />
  </div>
</template>
<script setup lang="ts">
  const value = defineModel<number | undefined>("id");

  const options = ref<{ value: number; label: string }[]>([]);
  onMounted(async () => {
    const { data } = await useApiFetch.queryBom();
    data.result.forEach((item) => {
      options.value.push({
        value: item.id,
        label: item.title,
      });
    });
  });
</script>
