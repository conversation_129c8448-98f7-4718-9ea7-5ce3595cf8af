<template>
  <a-select
    v-model:value="value"
    show-search
    placeholder="请选择"
    :options="options"
    :filter-option="filterOption"
    @focus="handleFocus"
    @blur="handleBlur"
    @change="handleChange"
  ></a-select>
</template>
<script lang="ts" setup>
  import type { SelectProps } from "ant-design-vue";
  import { ref } from "vue";
  const options = ref<SelectProps["options"]>([]);
  // const { data } = await useApiFetch.queryUserRoleList();
  const { data } = await useApiTrpc().admin.user.queryRole.query();
  type SelectOption = {
    value: number;
    label: string;
  };
  options.value = data.result.map((item: any) => {
    return {
      value: item.id,
      label: item.name,
    } as SelectOption;
  });
  const handleChange = (v: any) => {
    value.value = v;
    console.log(`selected ${v}`);
  };
  const handleBlur = () => {
    console.log("blur");
  };
  const handleFocus = async () => {
    console.log("focus");
    // const data = await useApi().get()("/api/usernamage/list");
  };
  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };
  const value = defineModel<number | undefined>("value", {
    required: true,
  });

  // const value = ref<string | undefined>(undefined);
</script>
