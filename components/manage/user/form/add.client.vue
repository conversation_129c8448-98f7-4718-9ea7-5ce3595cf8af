<template>
  <UiStoremanageForm
    :model="model"
    @submit="submit"
    :rules="rules"
    v-model:loading="loading"
    v-model:visible="visible"
  >
    <template #default="{ show }">
      <a-button @click="show">新建</a-button>
    </template>
    <template #form>
      <a-form-item label="编号" name="code">
        <a-input placeholder="请输入编号" v-model:value="model.code" />
      </a-form-item>
      <a-form-item label="姓名" name="name">
        <a-input placeholder="请输入姓名" v-model:value="model.name" />
      </a-form-item>
      <a-form-item label="用户名" name="username">
        <a-input
          placeholder="请输入用户名"
          v-model:value="model.username"
          autocomplete="new-password"
        />
      </a-form-item>
      <a-form-item label="密码" name="password">
        <a-input-password
          placeholder="请输入密码"
          v-model:value="model.password"
          autocomplete="new-password"
        />
      </a-form-item>
      <a-form-item label="确认密码" name="repassword">
        <a-input-password
          placeholder="请输入确认密码"
          v-model:value="model.repassword"
          autocomplete="new-password"
        />
      </a-form-item>
      <a-form-item label="角色" name="Role">
        <manage-user-role-selector
          v-model:value="model.Role"
        ></manage-user-role-selector>
      </a-form-item>
      <a-form-item label="邮箱" name="email">
        <a-input placeholder="请输入邮箱" v-model:value="model.email" />
      </a-form-item>
      <a-form-item label="联系电话" name="contactTel">
        <a-input
          placeholder="请输入联系电话"
          v-model:value="model.contactTel"
        />
      </a-form-item>
      <a-form-item label="锁定" name="lock">
        <a-switch v-model:checked="model.lock" />
      </a-form-item>
    </template>
  </UiStoremanageForm>
</template>
<script lang="ts" setup>
  import type { Rule } from "ant-design-vue/es/form";
  import { message } from "ant-design-vue";
  import { formRules } from "~/utils/formValidator";
  type UserForm = API.User.Create & {
    repassword: string;
  };
  const loading = ref(false);
  const visible = ref(false);
  const emits = defineEmits(["saveSuccess"]);
  const model = reactive<UserForm>({
    username: "",
    code: "",
    name: "",
    password: "",
    repassword: "",
    Role: "",
    email: "",
    contactTel: "",
    lock: false,
  });
  const rules = reactive(formRules.addUser);
  const submit = async (): Promise<void> => {
    const { code, message: msg, data } = await useApiFetch.addUser(model);
    if (code) {
      message.success(msg);
      emits("saveSuccess");
      useNuxtApp().$emitter.emit("formSaveSuccessed", data);
    } else {
      message.error(msg);
      loading.value = false;
    }
  };
</script>
