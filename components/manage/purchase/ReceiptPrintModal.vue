<template>
  <a-modal
    v-model:open="visible"
    title="打印入库单"
    width="80%"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="print-container">
      <!-- 打印按钮 -->
      <div class="print-actions" style="margin-bottom: 16px; text-align: right">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handlePrint">
            <template #icon>
              <PrinterOutlined />
            </template>
            打印
          </a-button>
        </a-space>
      </div>

      <!-- 打印内容 -->
      <div ref="printContentRef" class="print-content">
        <a-spin :spinning="loading">
          <div v-if="receiptData" class="receipt-document">
            <!-- 标题 -->
            <div class="signature-area">
              <div class="document-header">
                <h1>采购入库单</h1>
                <div class="document-info">
                  <p>采购单号： {{ receiptData.purchaseOrder?.orderNo }}</p>
                  <p>
                    货物来源单位：{{
                      receiptData.purchaseOrder?.supplier?.name
                    }}
                  </p>
                  <p>收货日期：{{ formatDate(receiptData.createdAt) }}</p>
                  <!-- <p>打印时间：{{ formatDate(new Date()) }}</p>
                <p>打印次数：{{ printCount }}</p>
                <p>打印人：{{ receiptData.user?.username }}</p> -->
                </div>
              </div>
            </div>

            <!-- 基本信息 -->
            <div class="basic-info">
              <!-- <a-descriptions :column="3" size="small">
                <a-descriptions-item label="采购订单号">
                  {{ receiptData.purchaseOrder?.orderNo }}
                </a-descriptions-item>
                <a-descriptions-item label="供应商:">
                  {{ receiptData.purchaseOrder?.supplier?.name }}
                </a-descriptions-item>
                <a-descriptions-item label="入库仓库">
                  {{ receiptData.purchaseOrder?.warehouse?.name }}
                </a-descriptions-item>
                <a-descriptions-item label="入库时间:">
                  {{ formatDate(receiptData.createdAt) }}
                </a-descriptions-item>
                <a-descriptions-item label="入库人员">
                  {{ receiptData.user?.username }}
                </a-descriptions-item>
                <a-descriptions-item label="备注">
                  {{ receiptData.note || "-" }}
                </a-descriptions-item>
              </a-descriptions> -->
            </div>

            <!-- 入库物料明细 -->
            <div class="items-detail">
              <!-- <h3>入库物料明细</h3> -->
              <a-table
                :dataSource="receiptData.items"
                :columns="printColumns"
                :pagination="false"
                size="small"
                bordered
                rowKey="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'materialInfo'">
                    <div>
                      <div>{{ record.orderItem?.material?.name }}</div>
                      <div style="color: #666; font-size: 12px">
                        编码：{{ record.orderItem?.material?.code }}
                      </div>
                    </div>
                  </template>
                </template>
                <template #summary>
                  <a-table-summary-row>
                    <a-table-summary-cell
                      :colSpan="5"
                      style="text-align: center"
                    >
                      总计
                    </a-table-summary-cell>
                    <a-table-summary-cell :colSpan="1" style="text-align: left">
                      {{
                        receiptData.items.reduce(
                          (sum: number, item: any) =>
                            sum + Number(item.receivedQuantity),
                          0
                        )
                      }}
                    </a-table-summary-cell>
                    <a-table-summary-cell :colSpan="1" style="text-align: left">
                      {{
                        receiptData.items
                          .reduce(
                            (sum: number, item: any) =>
                              sum +
                              Number(item.orderItem?.unitPrice) *
                                Number(item.receivedQuantity),
                            0
                          )
                          .toFixed(2)
                      }}
                    </a-table-summary-cell>
                  </a-table-summary-row>
                </template>
              </a-table>
            </div>

            <!-- 签名区域 -->
            <div class="signature-area">
              <div class="signature-row">
                <div class="signature-item">
                  <span>经手人：</span>
                  <span class="signature-line">{{
                    receiptData.user?.name
                  }}</span>
                </div>
                <div class="signature-item">
                  <span>财务主管：</span>
                  <span class="signature-line"></span>
                </div>
                <div class="signature-item">
                  <span>编号：</span>
                  <span class="signature-line"></span>
                </div>
              </div>
            </div>
          </div>
          <a-empty v-else description="未找到入库记录数据" />
        </a-spin>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, watch } from "vue";
  import { message } from "ant-design-vue";
  import { PrinterOutlined } from "@ant-design/icons-vue";
  import dayjs from "dayjs";
  import { title } from "process";

  // 定义props
  const props = defineProps({
    receiptId: {
      type: Number,
      default: null,
    },
    open: {
      type: Boolean,
      default: false,
    },
  });

  // 定义事件
  const emit = defineEmits(["update:open"]);

  // 状态变量
  const visible = ref(false);
  const loading = ref(false);
  const receiptData = ref<any>(null);
  const printContentRef = ref<HTMLElement>();

  // 表格列定义
  const printColumns = [
    // {
    //   title: "序号",
    //   key: "index",
    //   width: "60px",
    //   customRender: ({ index }: { index: number }) => index + 1,
    // },
    {
      title: "物料编码",
      dataIndex: ["orderItem", "material", "code"],
      key: "materialCode",
      width: "120px",
    },
    {
      title: "品名",
      key: "name",
      customRender: ({ record }: { record: any }) => {
        return record.orderItem?.material?.name || "-";
      },
      width: "200px",
    },
    {
      title: "规格",
      key: "spec",
      customRender: ({ record }: { record: any }) => {
        return record.orderItem?.material?.spec || "-";
      },
      width: "120px",
    },
    {
      title: "单位",
      key: "unit",
      dataIndex: ["orderItem", "material", "unit"],
      width: "80px",
    },
    {
      title: "单价",
      key: "unitPrice",
      customRender: ({ record }: { record: any }) => {
        const price = record.orderItem?.unitPrice;
        //保留两位小数,没有小数则补0
        return price ? Number(price).toFixed(2) : "-";
      },
      width: "120px",
    },
    {
      title: "数量",
      dataIndex: "receivedQuantity",
      key: "receivedQuantity",
      width: "100px",
    },
    {
      title: "总价",
      key: "totalPrice",
      customRender: ({ record }: { record: any }) => {
        const price = record.orderItem?.unitPrice;
        const quantity = record.receivedQuantity;
        const total = price && quantity ? Number(price) * Number(quantity) : 0;
        //保留两位小数,没有小数则补0
        return total.toFixed(2);
      },
      width: "120px",
    },
  ];

  // 格式化日期
  const formatDate = (date: string | Date) => {
    if (!date) return "-";
    return dayjs(date).format("YYYYMMDD");
  };

  // 获取入库记录详情
  const fetchReceiptDetail = async () => {
    if (!props.receiptId) return;

    loading.value = true;
    try {
      const result =
        await useApiTrpc().admin.purchase.getPurchaseReceiptDetail.query({
          id: props.receiptId,
        });

      if (result.code === 200) {
        receiptData.value = result.data;
      } else {
        message.error(result.message || "获取入库记录详情失败");
      }
    } catch (error) {
      console.error("获取入库记录详情失败", error);
      message.error("获取入库记录详情失败，请稍后再试");
    } finally {
      loading.value = false;
    }
  };

  // 打印处理
  const handlePrint = () => {
    if (!printContentRef.value) {
      message.error("打印内容未准备好");
      return;
    }

    // 创建新窗口进行打印
    const printWindow = window.open("", "_blank");
    if (!printWindow) {
      message.error("无法打开打印窗口，请检查浏览器设置");
      return;
    }

    // 获取打印内容
    const printContent = printContentRef.value.innerHTML;

    // 构建完整的HTML文档
    const printHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>丹东汉纳入库单</title>
          <style>
            ${getPrintStyles()}
          </style>
        </head>
        <body>
          ${printContent}
        </body>
      </html>
    `;

    printWindow.document.write(printHtml);
    printWindow.document.close();

    // 等待内容加载完成后打印
    printWindow.onload = () => {
      printWindow.print();
      printWindow.close();
    };
  };

  // 获取打印样式
  const getPrintStyles = () => {
    return `
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        color: #000;
      }
      .receipt-document {
        max-width: 800px;
        margin: 0 auto;
      }
      .document-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #000;
        padding-bottom: 20px;
      }
      .document-header h1 {
        margin: 0 0 10px 0;
        font-size: 24px;
        font-weight: bold;
      }
      .document-info {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
      }
      .document-info p {
        margin: 0;
        font-size: 14px;
      }
      .basic-info {
        margin-bottom: 30px;
      }
      .basic-info h3 {
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: bold;
      }
      .items-detail {
        margin-bottom: 30px;
      }
      .items-detail h3 {
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: bold;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }
      th, td {
        border: 1px solid #000;
        padding: 8px;
        text-align: left;
        font-size: 12px;
      }
      th {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: center;
      }
      .signature-area {
        margin-top: 40px;
        page-break-inside: avoid;
      }
      .signature-row {
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
      }
      .signature-item {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .signature-line {
        display: inline-block;
        width: 120px;
        border-bottom: 1px solid #000;
        height: 20px;
        text-align: center;
      }
      @media print {
        body {
          margin: 0;
        }
        .print-actions {
          display: none !important;
        }
      }
    `;
  };

  // 取消处理
  const handleCancel = () => {
    visible.value = false;
    emit("update:open", false);
  };

  // 监听props变化
  watch(
    () => props.open,
    (newValue) => {
      visible.value = newValue;
      if (newValue && props.receiptId) {
        fetchReceiptDetail();
      }
    },
    { immediate: true }
  );

  watch(
    () => props.receiptId,
    (newValue) => {
      if (newValue && visible.value) {
        fetchReceiptDetail();
      }
    }
  );
</script>

<style scoped>
  .print-container {
    max-height: 70vh;
    overflow-y: auto;
  }

  .print-content {
    background: white;
  }

  .receipt-document {
    padding: 20px;
    background: white;
  }

  .document-header {
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 2px solid #000;
    padding-bottom: 20px;
  }

  .document-header h1 {
    margin: 0 0 10px 0;
    font-size: 24px;
    font-weight: bold;
  }

  .document-info {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
  }

  .document-info p {
    margin: 0;
    font-size: 14px;
  }

  .basic-info {
    margin-bottom: 30px;
  }

  .items-detail {
    margin-bottom: 30px;
  }

  .items-detail h3 {
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: bold;
  }

  .signature-area {
    margin-top: 40px;
  }

  .signature-row {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
  }

  .signature-item {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .signature-line {
    display: inline-block;
    width: 120px;
    border-bottom: 1px solid #000;
    height: 20px;
    text-align: center;
  }

  :deep(.ant-descriptions-item-label) {
    font-weight: bold;
    background-color: #fafafa;
  }

  :deep(.ant-table-thead > tr > th) {
    background-color: #fafafa;
    font-weight: bold;
  }
</style>
