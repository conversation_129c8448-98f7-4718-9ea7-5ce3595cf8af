<template>
  <div class="receive-form">
    <a-spin :spinning="loading">
      <div v-if="orderData" class="order-info">
        <a-descriptions title="订单信息" bordered :column="2" size="small">
          <a-descriptions-item label="订单编号">{{
            orderData.orderNo
          }}</a-descriptions-item>
          <a-descriptions-item label="供应商">{{
            orderData.supplier?.name
          }}</a-descriptions-item>
          <a-descriptions-item label="总金额">{{
            formatCurrency(orderData.totalAmount)
          }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(orderData.status)">
              {{ getStatusText(orderData.status) }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 收货记录部分 -->
        <a-tabs v-model:activeKey="activeTabKey">
          <!-- 创建收货标签 -->
          <a-tab-pane key="create" tab="新增收货">
            <a-form :model="formState" ref="formRef" layout="vertical">
              <a-table
                :dataSource="orderItems"
                :columns="receiveColumns"
                :pagination="false"
                size="small"
                bordered
                rowKey="id"
              >
                <!-- 自定义列渲染 -->
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'material'">
                    {{ record.material?.name }}
                    <a-typography-text type="secondary"
                      >({{ record.material?.code }})</a-typography-text
                    >
                  </template>
                  <!-- 收货数量列 -->
                  <template v-if="column.key === 'receivedQuantity'">
                    <a-form-item
                      :name="['items', index, 'receivedQuantity']"
                      :rules="[
                        { required: true, message: '请输入收货数量' },
                        {
                          validator: (rule, value) =>
                            validateQuantity(rule, value, record),
                        },
                      ]"
                      class="no-margin"
                    >
                      <a-input-number
                        v-model:value="record.receivedQuantity"
                        @change="(value) => handleQuantityChange(value, index)"
                        placeholder="收货数量"
                        :min="0"
                        :max="record.remainingQuantity"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </template>

                  <!-- 批号列 -->
                  <template v-else-if="column.key === 'batchNo'">
                    <a-form-item
                      :name="['items', index, 'batchNo']"
                      :rules="[{ required: true, message: '请输入批号' }]"
                      class="no-margin"
                    >
                      <a-input
                        v-model:value="record.batchNo"
                        @change="
                          (e) => handleBatchNoChange(e.target.value, index)
                        "
                        placeholder="请输入批号"
                      />
                    </a-form-item>
                  </template>

                  <!-- 备注列 -->
                  <template v-else-if="column.key === 'note'">
                    <a-form-item
                      :name="['items', index, 'note']"
                      class="no-margin"
                    >
                      <a-input
                        v-model:value="record.note"
                        @change="(e) => handleNoteChange(e.target.value, index)"
                        placeholder="备注"
                      />
                    </a-form-item>
                  </template>
                </template>
              </a-table>

              <a-form-item
                name="note"
                label="收货备注"
                style="margin-top: 16px"
              >
                <a-textarea
                  v-model:value="formState.note"
                  placeholder="请输入收货备注信息"
                  :rows="2"
                />
              </a-form-item>

              <div class="form-actions">
                <a-space>
                  <a-button @click="handleCancel">取消</a-button>
                  <a-button
                    type="primary"
                    @click="handleSubmit"
                    :loading="submitting"
                    :disabled="!hasReceiveItems"
                  >
                    提交收货记录
                  </a-button>
                </a-space>
              </div>
            </a-form>
          </a-tab-pane>

          <!-- 历史收货记录标签 -->
          <a-tab-pane key="history" tab="历史收货记录">
            <a-empty
              v-if="receiptRecords.length === 0"
              description="暂无收货记录"
            />
            <a-collapse v-else>
              <a-collapse-panel
                v-for="(record, index) in receiptRecords"
                :key="record.id"
                :header="`收货记录 #${index + 1} - ${formatDate(
                  record.createdAt
                )}`"
              >
                <div class="receipt-record-header">
                  <div>
                    <p>收货人：{{ record.user?.username || "-" }}</p>
                    <p v-if="record.note">备注：{{ record.note }}</p>
                  </div>
                  <div>
                    <a-button
                      type="primary"
                      size="small"
                      @click="handlePrintReceipt(record.id)"
                    >
                      <template #icon>
                        <PrinterOutlined />
                      </template>
                      打印入库单
                    </a-button>
                  </div>
                </div>
                <a-table
                  :dataSource="record.items"
                  :columns="historyColumns"
                  :pagination="false"
                  rowKey="id"
                  size="small"
                  bordered
                >
                  <!-- 自定义列渲染 -->
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'materialName'">
                      {{ record.orderItem?.material?.name }}
                    </template>
                  </template>
                </a-table>
              </a-collapse-panel>
            </a-collapse>
          </a-tab-pane>
        </a-tabs>
      </div>
      <a-empty v-else description="未找到订单数据" />
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from "vue";
  import { message } from "ant-design-vue";
  import { PrinterOutlined } from "@ant-design/icons-vue";
  import dayjs from "dayjs";

  // 定义props
  const props = defineProps({
    orderId: {
      type: Number,
      required: true,
    },
  });

  // 定义事件
  const emit = defineEmits(["success", "cancel", "print-receipt"]);

  // 状态变量
  const loading = ref(false);
  const submitting = ref(false);
  const activeTabKey = ref("create");
  const orderData = ref<any>(null);
  const receiptRecords = ref<any[]>([]);

  // 订单物料项
  const orderItems = ref<any[]>([]);

  // 表单状态
  const formState = reactive({
    note: "",
    items: [] as any[],
  });

  // 表单引用
  const formRef = ref<any>();

  // 订单状态选项
  const orderStatus = [
    { label: "草稿", value: "draft" },
    { label: "待审核", value: "pending" },
    { label: "已审核", value: "approved" },
    { label: "已拒绝", value: "rejected" },
    { label: "部分收货", value: "partially_received" },
    { label: "完成", value: "completed" },
    { label: "取消", value: "cancelled" },
  ];

  // 根据状态值获取对应的文本
  const getStatusText = (status: string) => {
    const item = orderStatus.find((item) => item.value === status);
    return item ? item.label : "未知状态";
  };

  // 根据状态获取标签颜色
  const getStatusColor = (status: string) => {
    const statusColorMap: Record<string, string> = {
      draft: "default",
      pending: "processing",
      approved: "success",
      rejected: "error",
      partially_received: "warning",
      completed: "success",
      cancelled: "default",
    };
    return statusColorMap[status] || "default";
  };

  // 格式化函数
  const formatDate = (dateStr: string) => {
    if (!dateStr) return "-";
    return dayjs(dateStr).format("YYYY-MM-DD HH:mm:ss");
  };

  // const formatCurrency = (amount: number) => {
  //   return `¥ ${amount.toFixed(2)}`;
  // };
  const formatCurrency = (amount: any) => {
    if (amount === null || amount === undefined) return "¥ 0.00";
    const numAmount = Number(amount);
    return !isNaN(numAmount) ? `¥ ${numAmount.toFixed(2)}` : "¥ 0.00";
  };

  // 收货表格列定义
  const receiveColumns = [
    { title: "物料", dataIndex: ["material", "name"], key: "material" },
    { title: "规格", dataIndex: ["material", "spec"], key: "spec" },
    { title: "单位", dataIndex: ["material", "unit"], key: "unit" },
    { title: "订单数量", dataIndex: "quantity", key: "quantity" },
    { title: "已收货", dataIndex: "receivedQuantity", key: "alreadyReceived" },
    {
      title: "待收货",
      dataIndex: "remainingQuantity",
      key: "remainingQuantity",
    },
    { title: "本次收货", key: "receivedQuantity", width: "150px" },
    { title: "批号", key: "batchNo", width: "150px" },
    { title: "备注", key: "note", width: "200px" },
  ];

  // 历史记录表格列定义
  const historyColumns = [
    { title: "物料", key: "materialName" },
    {
      title: "收货数量",
      dataIndex: "receivedQuantity",
      key: "receivedQuantity",
    },
    { title: "批号", dataIndex: "batchNo", key: "batchNo" },
    { title: "备注", dataIndex: "note", key: "note" },
  ];

  // 收货数量验证
  const validateQuantity = (rule: any, value: number, record: any) => {
    if (value < 0) {
      return Promise.reject("收货数量不能小于0");
    }
    if (value > record.remainingQuantity) {
      return Promise.reject(
        `收货数量不能超过待收货数量 ${record.remainingQuantity}`
      );
    }
    return Promise.resolve();
  };

  // 计算属性
  const hasReceiveItems = computed(() => {
    // 修改为只要总体收货数量大于0即可（允许单个项目为0）
    const totalReceiveQuantity = orderItems.value.reduce(
      (sum, item) => sum + (Number(item.receivedQuantity) || 0),
      0
    );
    return totalReceiveQuantity > 0;
  });

  // 获取订单详情
  const fetchOrderDetail = async () => {
    loading.value = true;
    try {
      const result = await useApiTrpc().admin.purchase.getPurchaseOrder.query({
        id: props.orderId,
      });

      if (result.code === 200) {
        orderData.value = result.data;

        // 处理物料项
        const items = orderData.value.items.map((item: any) => {
          // 计算已收货数量
          let receivedQuantity = 0;
          if (orderData.value.receiptRecords) {
            orderData.value.receiptRecords.forEach((record: any) => {
              const recordItem = record.items.find(
                (ri: any) => ri.orderItemId === item.id
              );
              if (recordItem) {
                receivedQuantity += recordItem.receivedQuantity;
              }
            });
          }

          // 计算剩余可收货数量
          const remainingQuantity = item.quantity - receivedQuantity;

          return {
            ...item,
            alreadyReceived: receivedQuantity,
            remainingQuantity,
            receivedQuantity: remainingQuantity > 0 ? 0 : 0, // 本次收货默认为0
            note: "",
          };
        });

        // 过滤出还有剩余可收货数量的物料
        orderItems.value = items.filter(
          (item: any) => item.remainingQuantity > 0
        );

        // 同步数据到表单状态，解决验证问题
        formState.items = orderItems.value.map((item) => ({
          id: item.id,
          receivedQuantity: item.receivedQuantity,
          note: item.note,
          batchNo: item.batchNo,
        }));

        // 获取收货记录
        await fetchReceiptRecords();
      } else {
        message.error(result.message || "获取订单详情失败");
      }
    } catch (error) {
      console.error("获取订单详情失败", error);
      message.error("获取订单详情失败，请稍后再试");
    } finally {
      loading.value = false;
    }
  };

  // 获取收货记录
  const fetchReceiptRecords = async () => {
    try {
      const result = await useApiTrpc().admin.purchase.getReceiptRecords.query({
        orderId: props.orderId,
      });

      if (result.code === 200) {
        receiptRecords.value = result.data;
      } else {
        message.error(result.message || "获取收货记录失败");
      }
    } catch (error) {
      console.error("获取收货记录失败", error);
      message.error("获取收货记录失败，请稍后再试");
    }
  };

  // 处理数量变更，同步到表单状态
  const handleQuantityChange = (value: number | string, index: number) => {
    // 确保value是数字类型
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    formState.items[index].receivedQuantity = numValue;
  };

  // 处理备注变更，同步到表单状态
  const handleNoteChange = (value: string | undefined, index: number) => {
    formState.items[index].note = value || "";
  };

  // 处理批号变更，同步到表单状态
  const handleBatchNoChange = (value: string | undefined, index: number) => {
    formState.items[index].batchNo = value || "";
  };

  // 提交收货记录
  const handleSubmit = async () => {
    submitting.value = true;

    try {
      // 先确保表单数据与显示数据同步
      formState.items = orderItems.value.map((item, index) => ({
        id: item.id,
        receivedQuantity: item.receivedQuantity,
        batchNo: item.batchNo,
        note: item.note || "",
      }));

      // 表单验证
      const validationResult = await formRef.value
        .validate()
        .catch((errors: any) => {
          // 处理表单验证错误
          console.error("表单验证错误:", errors);
          const errorFields = errors.errorFields || [];
          if (errorFields.length > 0) {
            const firstError = errorFields[0];
            message.error(firstError.errors[0] || "表单填写有误，请检查输入");
          } else {
            message.error("表单验证失败，请检查输入");
          }
          return false;
        });

      // 如果验证失败直接返回
      if (validationResult === false) {
        submitting.value = false;
        return;
      }

      // 检查是否至少有一项物料有收货数量
      const totalReceiveQuantity = orderItems.value.reduce(
        (sum, item) => sum + (Number(item.receivedQuantity) || 0),
        0
      );

      if (totalReceiveQuantity <= 0) {
        message.warning("收货总数量必须大于0");
        submitting.value = false;
        return;
      }

      // 构建提交数据 - 只提交有录入的项目（包括数量为0的）
      const receiveItems = orderItems.value
        .map((item) => ({
          orderItemId: item.id,
          receivedQuantity: Number(item.receivedQuantity) || 0,
          batchNo: item.batchNo || "",
          note: item.note || "",
        }))
        .filter((item) => item.receivedQuantity >= 0); // 只过滤掉undefined或负数值

      // 构建提交数据
      const submitData = {
        purchaseOrderId: props.orderId,
        items: receiveItems,
        note: formState.note,
      };

      // 调用创建收货记录API
      const result =
        await useApiTrpc().admin.purchase.createPurchaseReceipt.mutate(
          submitData
        );

      if (result.code === 1) {
        // 检查是否所有物料都已完全收货
        let isFullyReceived = true;
        for (const item of orderItems.value) {
          const newReceivedQuantity =
            Number(item.alreadyReceived) + Number(item.receivedQuantity);
          if (newReceivedQuantity < Number(item.quantity)) {
            isFullyReceived = false;
            break;
          }
        }

        // 根据收货状态显示不同的成功提示
        if (isFullyReceived) {
          message.success("所有物料已完全入库");
        } else {
          message.success("收货记录创建成功");
        }

        emit("success");
      } else {
        message.error(result.message || "收货记录创建失败");
      }
    } catch (error: any) {
      console.error("提交收货记录失败", error);
      // 提供更详细的错误信息
      if (error.message) {
        message.error(`处理失败: ${error.message}`);
      } else {
        message.error("收货记录创建失败，请稍后重试");
      }
    } finally {
      submitting.value = false;
    }
  };

  // 取消操作
  const handleCancel = () => {
    emit("cancel");
  };

  // 打印入库单
  const handlePrintReceipt = (receiptId: number) => {
    emit("print-receipt", receiptId);
  };

  // 监听orderId变化
  watch(
    () => props.orderId,
    (newValue, oldValue) => {
      if (newValue !== oldValue && newValue) {
        fetchOrderDetail();
      }
    }
  );

  // 初始化
  onMounted(() => {
    fetchOrderDetail();
  });
</script>

<style scoped>
  .receive-form {
    padding: 0 10px;
  }

  .order-info {
    margin-bottom: 20px;
  }

  .form-actions {
    margin-top: 24px;
    text-align: right;
  }

  :deep(.no-margin) {
    margin-bottom: 0;
  }

  :deep(.ant-tabs-content) {
    padding-top: 16px;
  }

  .receipt-record-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
  }

  .receipt-record-header > div:first-child {
    flex: 1;
  }

  .receipt-record-header > div:last-child {
    margin-left: 16px;
  }
</style>
