<template>
  <div class="order-detail">
    <a-spin :spinning="loading">
      <template v-if="orderData">
        <!-- 订单基本信息 -->
        <div class="detail-section">
          <h3>订单基本信息</h3>
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="订单编号">{{
              orderData.orderNo
            }}</a-descriptions-item>
            <a-descriptions-item label="订单状态">
              <a-tag :color="getStatusColor(orderData.status)">
                {{ getStatusText(orderData.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="收货仓库">
              {{ orderData.warehouse?.name || "-" }}
            </a-descriptions-item>
            <a-descriptions-item label="供应商">{{
              orderData.supplier?.name
            }}</a-descriptions-item>
            <a-descriptions-item label="总金额">{{
              formatCurrency(orderData.totalAmount)
            }}</a-descriptions-item>
            <a-descriptions-item label="预计到货日期">{{
              orderData.expectedDeliveryDate || "-"
            }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{
              formatDate(orderData.createdAt)
            }}</a-descriptions-item>
            <a-descriptions-item label="创建人">{{
              orderData.user?.username || "-"
            }}</a-descriptions-item>
            <a-descriptions-item label="提交时间">{{
              orderData.submittedAt ? formatDate(orderData.submittedAt) : "-"
            }}</a-descriptions-item>
            <a-descriptions-item
              label="审批时间"
              :span="orderData.rejectReason ? 1 : 2"
            >
              {{
                orderData.approvedAt ? formatDate(orderData.approvedAt) : "-"
              }}
            </a-descriptions-item>
            <a-descriptions-item v-if="orderData.rejectReason" label="拒绝原因">
              {{ orderData.rejectReason }}
            </a-descriptions-item>
            <a-descriptions-item label="备注" :span="2">{{
              orderData.note || "-"
            }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 订单物料列表 -->
        <div class="detail-section">
          <h3>采购清单</h3>
          <a-table
            :dataSource="orderData.items || []"
            :columns="materialsColumns"
            :pagination="false"
            rowKey="id"
            size="small"
            bordered
          >
            <!-- 自定义列渲染 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'materialName'">
                {{ record.material?.name }}
              </template>
              <template v-else-if="column.key === 'materialSpec'">
                {{ record.material?.spec || "-" }}
              </template>
              <template v-else-if="column.key === 'materialUnit'">
                {{ record.material?.unit || "-" }}
              </template>
              <template v-else-if="column.key === 'totalPrice'">
                {{ formatCurrency(record.quantity * record.unitPrice) }}
              </template>
            </template>
          </a-table>
        </div>

        <!-- 收货记录 -->
        <div
          class="detail-section"
          v-if="orderData.receiptRecords && orderData.receiptRecords.length > 0"
        >
          <h3>收货记录</h3>
          <a-collapse>
            <a-collapse-panel
              v-for="(record, index) in orderData.receiptRecords"
              :key="record.id"
              :header="`收货记录 #${index + 1} - ${formatDate(
                record.createdAt
              )}`"
            >
              <p v-if="record.note">备注：{{ record.note }}</p>
              <a-table
                :dataSource="record.items || []"
                :columns="receiptColumns"
                :pagination="false"
                rowKey="id"
                size="small"
                bordered
              >
                <!-- 自定义列渲染 -->
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'materialName'">
                    {{ record.orderItem?.material?.name }}
                  </template>
                </template>
              </a-table>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </template>

      <!-- 无数据时显示 -->
      <a-empty v-else description="未找到订单数据" />
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from "vue";
  import { message } from "ant-design-vue";
  import dayjs from "dayjs";

  // 定义props
  const props = defineProps({
    orderId: {
      type: Number,
      required: true,
    },
  });

  // 订单状态定义
  const orderStatus = [
    { label: "草稿", value: "draft" },
    { label: "待审核", value: "pending" },
    { label: "已审核", value: "approved" },
    { label: "已拒绝", value: "rejected" },
    { label: "部分收货", value: "partially_received" },
    { label: "完成", value: "completed" },
    { label: "取消", value: "cancelled" },
  ];

  // 状态显示辅助函数
  const getStatusText = (status: string) => {
    const item = orderStatus.find((item) => item.value === status);
    return item ? item.label : "未知状态";
  };

  const getStatusColor = (status: string) => {
    const statusColorMap: Record<string, string> = {
      draft: "default",
      pending: "processing",
      approved: "success",
      rejected: "error",
      partially_received: "warning",
      completed: "success",
      cancelled: "default",
    };
    return statusColorMap[status] || "default";
  };

  // 格式化函数
  const formatDate = (dateStr: string) => {
    if (!dateStr) return "-";
    return dayjs(dateStr).format("YYYY-MM-DD HH:mm:ss");
  };

  const formatCurrency = (amount: any) => {
    if (amount === null || amount === undefined) return "¥ 0.00";
    const numAmount = Number(amount);
    return !isNaN(numAmount) ? `¥ ${numAmount.toFixed(2)}` : "¥ 0.00";
  };

  // 数据状态
  const loading = ref(false);
  const orderData = ref<any>(null);

  // 表格列定义
  const materialsColumns = [
    { title: "物料名称", dataIndex: "materialName", key: "materialName" },
    { title: "规格", dataIndex: "materialSpec", key: "materialSpec" },
    { title: "单位", dataIndex: "materialUnit", key: "materialUnit" },
    { title: "数量", dataIndex: "quantity", key: "quantity" },
    { title: "单价", dataIndex: "unitPrice", key: "unitPrice" },
    { title: "总价", dataIndex: "totalPrice", key: "totalPrice" },
    { title: "备注", dataIndex: "note", key: "note" },
  ];

  const receiptColumns = [
    { title: "物料名称", dataIndex: "materialName", key: "materialName" },
    {
      title: "收货数量",
      dataIndex: "receivedQuantity",
      key: "receivedQuantity",
    },
    { title: "批号", dataIndex: "batchNo", key: "batchNo" },
    { title: "备注", dataIndex: "note", key: "note" },
  ];

  // 获取订单详情
  const fetchOrderDetail = async () => {
    loading.value = true;
    try {
      const result = await useApiTrpc().admin.purchase.getPurchaseOrder.query({
        id: props.orderId,
      });

      if (result.code === 200) {
        orderData.value = result.data;
      } else {
        message.error(result.message || "获取订单详情失败");
      }
    } catch (error) {
      console.error("获取订单详情失败", error);
      message.error("获取订单详情失败，请稍后再试");
    } finally {
      loading.value = false;
    }
  };

  // 初始化
  onMounted(() => {
    fetchOrderDetail();
  });

  // 监听orderId变化
  watch(
    () => props.orderId,
    (newValue, oldValue) => {
      if (newValue !== oldValue && newValue) {
        fetchOrderDetail();
      }
    }
  );
</script>

<style scoped>
  .order-detail {
    padding: 0 10px;
  }

  .detail-section {
    margin-bottom: 24px;
  }

  .detail-section h3 {
    margin-bottom: 16px;
    font-weight: 600;
    font-size: 16px;
  }
</style>
