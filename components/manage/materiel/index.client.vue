<template>
  <div>
    <a-card title="物料管理">
      <template #extra>
        <manage-materiel-form-add
          @saveSuccess="saveSuccess"
        ></manage-materiel-form-add>
      </template>
      <ui-storemanage-table
        ref="tableRef"
        :columns="table.columns"
        :query="useApiFetch.queryMateriel"
        :model="table.model"
        rowKey="id"
        @expand="handleRowChange"
        v-model:expandedRowKeys="expanded"
      >
        <template #searchBox="">
          <a-form-item name="code" label="编码">
            <a-input v-model:value="table.model.code"></a-input>
          </a-form-item>
          <a-form-item name="name" label="名称">
            <a-input v-model:value="table.model.name"></a-input>
          </a-form-item>
          <a-form-item name="model" label="型号">
            <a-input v-model:value="table.model.model"></a-input>
          </a-form-item>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <a-tooltip :title="record.description" placement="right">
              <a>{{ record.name }}</a>
            </a-tooltip>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="viewBom(record)">查看BOM</a>
            </a-space>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <a-descriptions bordered>
            <a-descriptions-item label="物料编码">{{
              record.code
            }}</a-descriptions-item>
            <a-descriptions-item label="物料名称">{{
              record.name
            }}</a-descriptions-item>
            <a-descriptions-item label="物料型号">{{
              record.model
            }}</a-descriptions-item>
            <a-descriptions-item label="物料规格">{{
              record.sepc
            }}</a-descriptions-item>
            <a-descriptions-item label="计量单位">{{
              record.unit
            }}</a-descriptions-item>
            <a-descriptions-item label="物料描述">{{
              record.description
            }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{
              toBeijingTime(record.createAt)
            }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{
              toBeijingTime(record.updateAt)
            }}</a-descriptions-item>
          </a-descriptions>
        </template>
      </ui-storemanage-table>
      <a-modal v-model:open="visible" title="物料BOM" width="80%">
        <manage-materiel-bom :id="bom_id"></manage-materiel-bom>
      </a-modal>
    </a-card>
  </div>
</template>
<script setup lang="ts">
  import { Divider } from "ant-design-vue";
  import exp from "constants";
  import type { UnwrapRef } from "vue";

  interface SKU {
    title: string;
    unit: string;
    specification: string;
  }

  const tableRef = ref();

  const addSKU = (opt: any) => {
    console.log("addSKU", opt.record.id);
    // expanded.value.push(opt.record.id);
    // console.log(expanded.value);
  };
  const toBeijingTime = (time: string | Date) => {
    return new Date(time).toLocaleString("zh-CN", {
      timeZone: "Asia/Shanghai",
    });
  };
  const table = useTable<API.Materiel.Query.Result>({
    columns: [
      { title: "编码", dataIndex: "code", key: "code" },
      { title: "名称", dataIndex: "name", key: "name" },
      { title: "型号", dataIndex: "model", key: "model" },
      { title: "单位", dataIndex: "unit", key: "unit" },
      { title: "规格", dataIndex: "sepc", key: "sepc" },
      {
        title: "状态",
        dataIndex: "useable",
        key: "useable",
        customRender: (opt) => {
          switch (opt.record.useable) {
            case true:
              return "启用";
            case false:
              return "停用";
            default:
              return "未知";
          }
        },
      },
      {
        title: "创建时间",
        dataIndex: "createAt",
        key: "createAt",
        customRender: (opt) => {
          return toBeijingTime(opt.record.createAt);
        },
      },
      {
        title: "更新时间",
        dataIndex: "updateAt",
        key: "updateAt",
        customRender: (opt) => {
          return toBeijingTime(opt.record.updateAt);
        },
      },
      {
        title: "操作",
        dataIndex: "action",
        key: "action",
      },
    ],
  });
  const extendsTable = useTable({
    columns: [
      { title: "SKU名称", dataIndex: "title" },
      { title: "单位", dataIndex: "unit" },
      { title: "规格", dataIndex: "specification" },
      // {
      //   title: "操作",
      //   dataIndex: "action",
      //   customRender: (opt) => {
      //     return h("a", "停用");
      //   },
      // },
    ],
  });
  const expanded = ref<number[]>([]);
  const visible = ref(false);
  const bom_id = ref(0);
  const viewBom = (record: API.Materiel.Query.Result) => {
    bom_id.value = record.id;
    visible.value = true;
    console.log(bom_id.value);
  };
  // const extendsTableData = ref();
  const handleRowChange = async (
    expandedRows: boolean,
    record: API.Materiel.Query.Result
  ) => {
    // console.log("handleRowChange", expandedRows, record);
    if (expandedRows) {
      console.log(expanded.value, expandedRows, record);
    }
  };
  const saveSuccess = () => {
    tableRef.value.query();
  };
</script>
