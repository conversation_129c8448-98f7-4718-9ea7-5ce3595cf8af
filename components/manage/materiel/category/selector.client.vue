<template>
  <!-- <a-cascader v-model:value="value" :options="options" :load-data="loadData" placeholder="Please select"
        style="width: 100%" @change="handleChange">
    </a-cascader> -->
  <a-space>
    <a-select
      v-model:value="value[0]"
      placeholder="请选择品类分类"
      style="width: 120px"
    >
      <a-select-option value="01">01滤器</a-select-option>
      <a-select-option value="02">02培养基</a-select-option>
      <a-select-option value="03">03试剂</a-select-option>
      <a-select-option value="04">04实验室耗材</a-select-option>
      <a-select-option value="05">05仪器</a-select-option>
      <a-select-option value="06">06标准物质</a-select-option>
      <a-select-option value="FF">FF其他</a-select-option>
    </a-select>
    <a-select
      v-model:value="value[1]"
      placeholder="请选择属性分类"
      style="width: 120px"
    >
      <a-select-option value="01">01原材料</a-select-option>
      <a-select-option value="02">02半成品</a-select-option>
      <a-select-option value="03">03组件</a-select-option>
      <a-select-option value="04">04半成品</a-select-option>
      <a-select-option value="05">05产成品</a-select-option>
      <a-select-option value="06">06商品成品</a-select-option>
      <a-select-option value="FF">FF其他</a-select-option>
    </a-select>
    <a-select
      v-model:value="value[2]"
      placeholder="请选择保留位数"
      style="width: 120px"
    >
      <a-select-option value="00">00</a-select-option>
    </a-select>
  </a-space>
</template>
<script setup lang="ts">
  interface Option {
    value?: string | number | null | undefined;
    label?: string;
    loading?: boolean;
    isLeaf?: boolean;
    children?: Option[];
    code?: string;
  }
  const options = ref<Option[]>([]);

  const value = defineModel<[string, string, string]>("value", {
    required: true,
    default: ["01", "01", "00"],
  });
</script>
