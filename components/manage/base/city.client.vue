<!-- 
  中国行政区划选择组件
  使用Ant Design Vue的级联选择器实现省市区选择
  支持:
  - 省市区三级联动选择
  - 可根据需要选择级别(省/省市/省市区)
  - 支持默认值设置
-->
<script setup lang="ts">
  import { computed, ref, watch, onMounted } from "vue";
  import axios from "axios";
  import type { CascaderProps } from "ant-design-vue";
  import cityData from "@/public/city.json";

  // 定义行政区划选项类型
  interface AreaOption {
    value: string;
    label: string;
    children?: AreaOption[];
  }

  // 定义组件接收的属性
  const props = defineProps({
    type: {
      type: String as PropType<"selector" | "text">,
      default: "selector",
    },
    // 选择级别: 1-省 2-市 3-区/县
    level: {
      type: Number,
      default: 3,
    },
    // 是否允许清空选择
    allowClear: {
      type: Boolean,
      default: true,
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 占位文本
    placeholder: {
      type: String,
      default: "请选择省/市/区",
    },
    // 是否显示搜索框
    showSearch: {
      type: Boolean,
      default: true,
    },
  });

  const value = defineModel<string[]>("value", {
    default: () => [] as string[],
  });

  // 行政区划数据
  const areas = ref<AreaOption[]>([]);
  // 加载状态
  const loading = ref(false);

  // 过滤选项方法，用于搜索功能
  const filter = (inputValue: string, path: AreaOption[]) => {
    return path.some(
      (option) =>
        option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1
    );
  };

  // 这里使用一个常用的中国行政区划API，也可以替换为自己的数据源
  //   const response = await axios.get(
  //     "https://unpkg.com/china-area-data/data.json"
  //   );
  //   const rawData = response.data;
  const rawData = cityData as any;

  // 加载行政区划数据
  const loadAreaData = async () => {
    try {
      loading.value = true;

      // 处理原始数据，转换为级联选择器所需格式
      const provinces: AreaOption[] = Object.keys(rawData["86"]).map(
        (provinceCode) => {
          const province: AreaOption = {
            value: provinceCode,
            label: rawData["86"][provinceCode],
            children: [],
          };

          // 如果需要市级，则添加市级数据
          if (props.level >= 2 && rawData[provinceCode]) {
            province.children = Object.keys(rawData[provinceCode]).map(
              (cityCode) => {
                const city: AreaOption = {
                  value: cityCode,
                  label: rawData[provinceCode][cityCode],
                  children: [],
                };

                // 如果需要区/县级，则添加区县数据
                if (props.level >= 3 && rawData[cityCode]) {
                  city.children = Object.keys(rawData[cityCode]).map(
                    (areaCode) => {
                      return {
                        value: areaCode,
                        label: rawData[cityCode][areaCode],
                      };
                    }
                  );
                }

                return city;
              }
            );
          }

          return province;
        }
      );

      areas.value = provinces;
    } catch (error) {
      console.error("加载行政区划数据失败:", error);
    } finally {
      loading.value = false;
    }
  };

  // 计算级联选择器显示的字段
  const displayRender = ({ labels }: { labels: string[] }) => {
    return labels.join(" / ");
  };

  // 根据城市代码计算城市名称
  const getCityNameByCode = (code: string): string => {
    if (!code || !rawData) return "";

    // 省级
    if (code.endsWith("0000")) {
      return rawData["86"][code] || "";
    }

    // 市级
    if (code.endsWith("00")) {
      const provinceCode = code.substring(0, 2) + "0000";
      return rawData[provinceCode]?.[code] || "";
    }

    // 区县级
    const cityCode = code.substring(0, 4) + "00";
    return rawData[cityCode]?.[code] || "";
  };
  const getComplateCityName = (code: string[]) => {
    if (code.length === 1) {
      return [getCityNameByCode(code[0])];
    } else if (code.length === 2) {
      return [getCityNameByCode(code[0]), getCityNameByCode(code[1])];
    } else if (code.length === 3) {
      return [
        getCityNameByCode(code[0]),
        getCityNameByCode(code[1]),
        getCityNameByCode(code[2]),
      ];
    }
    return [];
  };
  // 组件挂载时加载数据
  onMounted(loadAreaData);
</script>

<template>
  <div class="area-selector">
    <template v-if="props.type === 'selector'">
      <a-cascader
        v-model:value="value"
        :options="areas"
        :placeholder="placeholder"
        :show-search="showSearch"
        :filter-option="filter"
        :disabled="disabled || loading"
        :allow-clear="allowClear"
        :display-render="displayRender"
        :loading="loading"
        class="w-full"
      />
    </template>
    <template v-if="props.type === 'text'">
      <slot name="text" :cityName="getComplateCityName(value)">
        {{ getComplateCityName(value).join("/") }}
      </slot>
    </template>
  </div>
</template>

<style scoped>
  .area-selector {
    width: 100%;
  }
</style>
