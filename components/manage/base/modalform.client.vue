<script lang="ts" setup>
  import type { FormExpose } from "ant-design-vue/es/form/Form";

  /**
   * 使用Ant Design Form,实现系统中通用的表单
   */
  //   const emits = defineEmits(['click']);
  const loading = defineModel<boolean>("loading", { default: false });
  const visible = defineModel<boolean>("visible", { default: false });
  const submitText = computed(() => {
    return loading.value ? "保存中,请勿操作..." : "提交";
  });

  const emits = defineEmits(["submit", "show"]);
  const attrs = useAttrs();
  const filteredAttrs = computed(() => {
    return {
      ...attrs,
      visible: undefined,
      title: undefined,
      ref: undefined,
      submit: undefined,
    };
  });
  const formEl = ref<FormExpose>();
  const props = defineProps({
    title: {
      type: String,
      default: "表单",
    },
    model: {
      type: Object,
      required: true,
    },
  });
  const show = () => {
    visible.value = true;
    emits("show");
  };
  const close = () => {
    visible.value = false;
  };
  const reset = () => {
    formEl.value!.resetFields();
  };
  watch(
    () => visible.value,
    (val) => {
      if (!val) {
        reset();
      }
    }
  );

  const submit = async () => {
    // loading.value = true;
    formEl
      .value!.validate()
      .then(() => {
        loading.value = true;
        emits("submit");
      })
      .catch((error: any) => {
        loading.value = false;
        console.log("error", error);
      });
  };

  onMounted(() => {
    useNuxtApp().$emitter.on("formSaveSuccessed", () => {
      loading.value = false;
      visible.value = false;
    });
  });
</script>

<template>
  <div v-bind="$attrs"></div>
  <slot :show="show" :close="close"></slot>
  <a-drawer
    :title="props.title"
    v-model:open="visible"
    :width="600"
    destroyOnClose
    :maskClosable="false"
    :closable="false"
  >
    <a-form
      v-bind="filteredAttrs"
      :model="props.model"
      ref="formEl"
      layout="vertical"
    >
      <slot name="form"></slot>
    </a-form>
    <template #extra>
      <a-space>
        <a-button @click="close">取消</a-button>
        <a-button danger @click="reset">重置表单</a-button>
        <a-button type="primary" @click="submit" :loading="loading">{{
          submitText
        }}</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
