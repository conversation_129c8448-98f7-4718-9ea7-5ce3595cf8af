<template>
  <div>
    <a-button type="dashed" block @click="handleAddMaterial">
      <plus-outlined /> 添加
    </a-button>
    <a-list :data-source="materials">
      <template #renderItem="{ item, index }">
        <a-list-item>
          <template #actions>
            <a-input-number
              v-model:value="item.quantity"
              :min="1"
              placeholder="请输入数量"
            />
            <a-button type="link" danger @click="removeMaterial(index)"
              >删除</a-button
            >
          </template>
          <a-list-item-meta>
            <template #title
              >{{ item.Product.title }}({{ item.Product.code }})-{{
                item.Product.model
              }}</template
            >
            <template #description
              >{{ item.title }}-{{ item.unit }}-{{
                item.specification
              }}</template
            >
          </a-list-item-meta>
        </a-list-item>
      </template>
    </a-list>
    <model-materiel
      @selected="onMaterielSelect"
      :multiple="true"
      v-model:open="materielModalOpen"
    />
  </div>
</template>
<script setup lang="ts">
  import ModelMateriel from "~/components/modal/materiel.client.vue";

  const materielModalOpen = ref(false);
  const handleAddMaterial = () => {
    console.log("添加物料");
    materielModalOpen.value = true;
  };
  const materials = defineModel<API.BomCreateInput["materials"]>("materials", {
    required: true,
    default: [],
  });
  const removeMaterial = (index: number) => {
    materials.value.splice(index, 1);
  };
  const onMaterielSelect = (materiel: API.BomCreateInput["materials"]) => {
    materiel.forEach((item) => {
      materials.value.push(item);
    });
  };
</script>
