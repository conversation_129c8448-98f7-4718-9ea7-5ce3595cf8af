<template>
  <UiStoremanageTable
    :columns="table.columns"
    :query="useApiFetch.queryMateriel"
    :model="table.model"
    rowKey="id"
    @expand="handleRowChange"
    v-model:expandedRowKeys="expanded"
  >
    <template #searchBox="">
      <a-form-item name="code" label="编码">
        <a-input v-model:value="table.model.code"></a-input>
      </a-form-item>
      <a-form-item name="title" label="名称">
        <a-input v-model:value="table.model.title"></a-input>
      </a-form-item>
      <a-form-item name="model" label="型号">
        <a-input v-model:value="table.model.model"></a-input>
      </a-form-item>
    </template>
    <template #expandedRowRender="{ record }">
      <a-row type="flex" justify="center" align="middle">
        <a-col style="width: 95%">
          <a-table
            size="small"
            :pagination="false"
            :bordered="true"
            :columns="extendsTable.columns"
            :dataSource="record.productSKU"
          ></a-table>
        </a-col>
      </a-row>
    </template>
  </UiStoremanageTable>
</template>
<script setup lang="ts">
  import { Divider } from "ant-design-vue";
  import exp from "constants";
  import type { UnwrapRef } from "vue";
  import FormMaterielEdit from "~/components/form/materiel/edit.client.vue";

  interface SKU {
    title: string;
    unit: string;
    specification: string;
  }

  const addSKU = (opt: any) => {
    console.log("addSKU", opt.record.id);
    // expanded.value.push(opt.record.id);
    // console.log(expanded.value);
  };

  const table = useTable<
    API.ProductCreateInput & { id: number; productSKU: SKU[] }
  >({
    columns: [
      { title: "编码", dataIndex: "code", key: "code" },
      { title: "名称", dataIndex: "title", key: "title" },
      { title: "型号", dataIndex: "model", key: "model" },
      { title: "单位", dataIndex: "unit", key: "unit" },
      {
        title: "状态",
        dataIndex: "lock",
        key: "lock",
        customRender: (opt) => {
          switch (opt.record.lock) {
            case false:
              return "可用";
            case true:
              return "停用";
            default:
              return "未知";
          }
        },
      },
      {
        title: "操作",
        dataIndex: "action",
        key: "action",
        customRender: (opt) => {
          return [
            h(FormMaterielEdit, {
              model: opt.record,
              onSaveSuccess: () => {},
            }),
            // h("a", { onClick: () => detail(opt) }, "详情"),
            // h(Divider, { type: "vertical" }),
            // h("a", "停用"),
          ];
        },
      },
    ],
  });
  const extendsTable = useTable({
    columns: [
      { title: "SKU名称", dataIndex: "title" },
      { title: "单位", dataIndex: "unit" },
      { title: "规格", dataIndex: "specification" },
      // {
      //   title: "操作",
      //   dataIndex: "action",
      //   customRender: (opt) => {
      //     return h("a", "停用");
      //   },
      // },
    ],
  });
  const expanded = ref<number[]>([]);
  // const extendsTableData = ref();
  const handleRowChange = async (
    expandedRows: boolean,
    record: API.MaterielReturnTypeResult
  ) => {
    // console.log("handleRowChange", expandedRows, record);
    if (expandedRows) {
      console.log(expanded.value, expandedRows, record);
    }
  };
</script>
