<template>
  <div class="supplier-container">
    <a-card title="供应商管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>新增供应商
          </a-button>
        </a-space>
      </template>
      <!-- 表格 -->
      <manage-base-table
        ref="tableRef"
        :columns="columns"
        :model="searchForm"
        :query="query"
        rowKey="id"
        bordered
      >
        <template #searchBox>
          <a-form-item name="name" label="供应商名称">
            <manage-base-search-input
              v-model:value="searchForm.name"
              placeholder="请输入供应商名称"
            />
          </a-form-item>
          <a-form-item name="contactName" label="联系人">
            <a-input
              v-model:value="searchForm.contactName"
              placeholder="请输入联系人"
            />
          </a-form-item>
          <a-form-item name="address" label="供应商地址">
            <a-input
              v-model:value="searchForm.address"
              placeholder="请输入供应商地址"
            />
          </a-form-item>
          <a-form-item name="lock" label="状态">
            <a-select
              v-model:value="searchForm.lock"
              placeholder="请选择状态"
              style="width: 150px"
              allowClear
            >
              <a-select-option :value="true">锁定</a-select-option>
              <a-select-option :value="false">正常</a-select-option>
            </a-select>
          </a-form-item>
        </template>
        <!-- 状态列自定义渲染 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'citycode'">
            <manage-base-city
              :level="3"
              v-model:value="record.citycode"
              type="text"
            />
          </template>
          <template v-if="column.dataIndex === 'lock'">
            <a-tag :color="record.lock ? 'error' : 'success'">
              {{ record.lock ? "锁定" : "正常" }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除该供应商吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a class="danger-text">删除</a>
              </a-popconfirm>
              <a-divider type="vertical" />
              <a @click="handleToggleLock(record)">
                {{ record.lock ? "解锁" : "锁定" }}
              </a>
            </a-space>
          </template>
        </template>
      </manage-base-table>
    </a-card>

    <!-- 新增/编辑供应商弹窗 -->
    <a-modal
      v-model:open="modalOpen"
      :title="modalTitle"
      :maskClosable="false"
      :closable="true"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="供应商名称" name="name">
          <a-input
            v-model:value="formState.name"
            placeholder="请输入供应商名称"
          />
        </a-form-item>
        <a-form-item label="城市代码" name="citycode">
          <manage-base-city :level="3" v-model:value="formState.citycode" />
        </a-form-item>
        <a-form-item label="供应商地址" name="address">
          <a-textarea
            v-model:value="formState.address"
            placeholder="请输入供应商地址"
            :rows="3"
          />
        </a-form-item>
        <a-form-item label="联系人" name="contactName">
          <a-input
            v-model:value="formState.contactName"
            placeholder="请输入联系人"
          />
        </a-form-item>
        <a-form-item label="联系电话" name="contactPhone">
          <a-input
            v-model:value="formState.contactPhone"
            placeholder="请输入联系电话"
          />
        </a-form-item>
        <a-form-item label="电子邮箱" name="email">
          <a-input
            v-model:value="formState.email"
            placeholder="请输入电子邮箱"
          />
        </a-form-item>
        <a-form-item label="状态" name="lock">
          <a-switch v-model:checked="formState.lock" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSave" :loading="saveLoading">
          保存
        </a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { PlusOutlined, ReloadOutlined } from "@ant-design/icons-vue";
  import { reactive, ref, onMounted } from "vue";
  import { notification } from "ant-design-vue";
  import type {
    TableColumnsType,
    TableProps,
    SelectProps,
  } from "ant-design-vue";
  import type { Rule } from "ant-design-vue/es/form";
  import type { FormInstance } from "ant-design-vue/es/form";
  import type { SelectValue } from "ant-design-vue/es/select";
  import type { TableInstance } from "@/components/manage/base/table.client.vue";
  // 定义供应商接口
  interface Supplier {
    id: number;
    name: string;
    citycode: string[];
    address: string;
    contactName: string;
    contactPhone: string;
    email: string;
    createAt: string;
    updateAt: string;
    lock: boolean;
  }

  const tableRef = ref<TableInstance>();

  // 搜索表单数据
  const searchForm = reactive({
    name: undefined as string | undefined,
    address: undefined as string | undefined,
    contactName: undefined as string | undefined,
    lock: undefined as SelectValue,
  });

  // 表格相关数据
  const loading = ref(false);
  const tableData = ref<Supplier[]>([]);
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total: number) => `共 ${total} 条`,
  });

  // 表格列定义 ExtendedTableColumnProps
  const columns: ExtendedTableColumnProps<Supplier>[] = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80,
    },
    { title: "供应商名称", dataIndex: "name", key: "name" },
    { title: "城市代码", dataIndex: "citycode", key: "citycode" },
    { title: "供应商地址", dataIndex: "address", key: "address" },
    { title: "联系人", dataIndex: "contactName", key: "contactName" },
    { title: "联系电话", dataIndex: "contactPhone", key: "contactPhone" },
    { title: "电子邮箱", dataIndex: "email", key: "email" },
    {
      title: "创建时间",
      dataIndex: "createAt",
      key: "createAt",
      defaultVisible: false,
    },
    {
      title: "更新时间",
      dataIndex: "updateAt",
      key: "updateAt",
      defaultVisible: false,
    },
    { title: "状态", dataIndex: "lock", key: "lock", width: 100 },
    {
      title: "操作",
      key: "action",
      width: 200,
      align: "center" as const,
    },
  ];

  // 弹窗相关
  const modalOpen = ref(false);
  const modalTitle = ref("新增供应商");
  const saveLoading = ref(false);
  const formRef = ref<FormInstance>();
  const formState = reactive({
    id: null as number | null,
    name: "",
    citycode: [],
    address: "",
    contactName: "",
    contactPhone: "",
    email: "",
    lock: false,
  });

  // 表单验证规则
  const rules = {
    name: [
      { required: true, message: "请输入供应商名称", trigger: "blur" },
    ] as Rule[],
    citycode: [
      {
        validator: (rule, value) => {
          return new Promise((resolve, reject) => {
            if (value && value.length === 0) {
              reject("请输入城市代码");
            } else {
              resolve();
            }
          });
        },
        trigger: "change",
      },
    ] as Rule[],
    contactPhone: [
      // { required: true, message: "请输入联系电话", trigger: "blur" },
      // {
      //   pattern: /^1[3-9]\d{9}$/,
      //   message: "请输入正确的手机号码",
      //   trigger: "blur",
      // },
      {
        validator: (rule, value) => {
          return new Promise((resolve, reject) => {
            if (value && value.length > 16) {
              reject("请输入小于16位的号码");
            } else {
              resolve();
            }
          });
        },
        trigger: "blur",
      },
    ] as Rule[],
    email: [
      { type: "email", message: "请输入正确的电子邮箱格式", trigger: "blur" },
    ] as Rule[],
  };
  const query = useApiTrpc().admin.purchase.querySupplier.query;
  // 添加TRPC接口引用
  const trpc = useApiTrpc();
  const addSupplierMutation = trpc.admin.purchase.addSupplier.mutate;
  const updateSupplierMutation = trpc.admin.purchase.updateSupplier.mutate;
  const deleteSupplierMutation = trpc.admin.purchase.deleteSupplier.mutate;
  const toggleSupplierLockMutation =
    trpc.admin.purchase.toggleSupplierLock.mutate;

  // 新增供应商
  const handleAdd = () => {
    modalTitle.value = "新增供应商";
    formState.id = null;
    formState.name = "";
    formState.citycode = [];
    formState.address = "";
    formState.contactName = "";
    formState.contactPhone = "";
    formState.email = "";
    formState.lock = false;
    if (formRef.value) {
      formRef.value.resetFields();
    }
    modalOpen.value = true;
  };

  // 编辑供应商
  const handleEdit = (record: Record<string, any>) => {
    modalTitle.value = "编辑供应商";
    const supplier: Supplier = {
      id: record.id,
      name: record.name,
      citycode: record.citycode,
      address: record.address,
      contactName: record.contactName,
      contactPhone: record.contactPhone,
      email: record.email,
      createAt: record.createAt,
      updateAt: record.updateAt,
      lock: record.lock,
    };
    Object.assign(formState, supplier);
    modalOpen.value = true;
  };

  // 删除供应商
  const handleDelete = async (record: Record<string, any>) => {
    try {
      // 调用删除供应商接口
      await deleteSupplierMutation({ id: record.id });

      notification.success({
        message: "删除成功",
        description: `已成功删除供应商：${record.name}`,
      });

      // 刷新表格数据
      tableRef.value?.query();
    } catch (error) {
      console.error("删除供应商失败:", error);
      notification.error({
        message: "删除供应商失败",
        description: "服务异常，请稍后重试",
      });
    }
  };

  // 锁定/解锁供应商
  const handleToggleLock = async (record: Record<string, any>) => {
    try {
      // 调用切换供应商锁定状态接口
      await toggleSupplierLockMutation({
        id: record.id,
        lock: !record.lock,
      });

      notification.success({
        message: record.lock ? "解锁成功" : "锁定成功",
        description: `已成功${record.lock ? "解锁" : "锁定"}供应商：${
          record.name
        }`,
      });

      // 刷新表格数据
      tableRef.value?.query();
    } catch (error) {
      console.error("更新供应商状态失败:", error);
      notification.error({
        message: "更新供应商状态失败",
        description: "服务异常，请稍后重试",
      });
    }
  };

  // 保存供应商
  const handleSave = async () => {
    if (!formRef.value) return;

    try {
      await formRef.value.validate();
      saveLoading.value = true;

      // 准备提交的数据
      const supplierData = {
        name: formState.name,
        citycode: formState.citycode,
        address: formState.address,
        contactName: formState.contactName,
        contactPhone: formState.contactPhone,
        email: formState.email,
        lock: formState.lock,
      };

      if (formState.id) {
        // 编辑供应商
        await updateSupplierMutation({
          id: formState.id,
          ...supplierData,
        });
      } else {
        // 新增供应商
        await addSupplierMutation(supplierData);
      }

      notification.success({
        message: "保存成功",
        description: `已成功${formState.id ? "更新" : "新增"}供应商：${
          formState.name
        }`,
      });

      modalOpen.value = false;
      // 刷新表格数据
      tableRef.value?.query();
    } catch (error) {
      console.error("保存供应商失败:", error);

      // 错误处理，显示服务器返回的错误信息
      if (error && typeof error === "object" && "message" in error) {
        notification.error({
          message: "保存失败",
          description: (error as any).message || "服务异常，请稍后重试",
        });
      } else {
        notification.error({
          message: "保存失败",
          description: "服务异常，请稍后重试",
        });
      }
    } finally {
      saveLoading.value = false;
    }
  };

  // 取消弹窗
  const handleCancel = () => {
    modalOpen.value = false;
  };

  // 初始化
  onMounted(() => {
    tableRef.value?.query();
  });
</script>

<style scoped>
  /* .supplier-container {
    padding: 0 10px;
  } */

  .table-operations {
    margin-bottom: 16px;
    margin-top: 16px;
  }

  .danger-text {
    color: #ff4d4f;
  }

  :deep(.ant-table-pagination.ant-pagination) {
    margin: 16px 0;
  }
</style>
