<template>
  <div class="purchase-order-container">
    <a-card title="采购订单管理" :bordered="false">
      <!-- 搜索区域 -->
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item name="orderNo" label="订单编号">
          <a-input
            v-model:value="searchForm.orderNo"
            placeholder="请输入订单编号"
          />
        </a-form-item>
        <a-form-item name="supplierName" label="供应商">
          <a-input
            v-model:value="searchForm.supplierName"
            placeholder="请输入供应商名称"
          />
        </a-form-item>
        <a-form-item name="status" label="订单状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            style="width: 150px"
            allowClear
          >
            <a-select-option
              v-for="item in orderStatus"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="dateRange" label="创建日期">
          <a-range-picker
            v-model:value="searchForm.dateRange"
            valueFormat="YYYY-MM-DD"
          />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit">查询</a-button>
            <a-button @click="resetSearch">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <!-- 操作按钮 -->
      <div class="table-operations">
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>新增订单
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>刷新
          </a-button>
        </a-space>
      </div>

      <!-- 表格 -->
      <manage-base-table
        ref="tableRef"
        :query="query"
        :columns="columns"
        rowKey="id"
      >
        <!-- 状态列自定义渲染 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <!-- 操作列 -->
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleView(record)">查看</a>
              <a
                @click="handleEdit(record)"
                v-if="['draft', 'rejected'].includes(record.status)"
                >编辑</a
              >
              <a-popconfirm
                title="确定要删除该订单吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
                v-if="record.status === 'draft'"
              >
                <a class="danger-text">删除</a>
              </a-popconfirm>
              <a @click="handleSubmit(record)" v-if="record.status === 'draft'"
                >提交审核</a
              >
              <!-- <a
                @click="handleReceive(record)"
                v-if="
                  ['approved', 'partially_received'].includes(record.status)
                "
              >
                收货记录
              </a> -->
              <!-- 添加审核按钮 -->
              <a @click="handleAudit(record)" v-if="record.status === 'pending'"
                >审核</a
              >
            </a-space>
          </template>
        </template>
      </manage-base-table>
    </a-card>

    <!-- 表单弹窗组件 -->
    <a-modal
      v-model:open="modalOpen"
      :title="modalTitle"
      :width="800"
      :footer="null"
      @cancel="handleCancel"
    >
      <!-- 表单组件或详情组件会在这里显示 -->
      <template v-if="modalType === 'view'">
        <ManagePurchaseOrderDetail :order-id="currentRecord.id" />
      </template>
      <template v-else-if="modalType === 'form'">
        <ManagePurchaseOrderForm
          :order-data="currentRecord"
          :mode="currentRecord.id ? 'edit' : 'add'"
          @success="handleFormSuccess"
          @cancel="handleCancel"
        />
      </template>
      <template v-else-if="modalType === 'receive'">
        <ManagePurchaseOrderReceiveForm
          :order-id="currentRecord.id"
          @success="handleReceiveSuccess"
          @cancel="handleCancel"
        />
      </template>
      <!-- 添加审核表单 -->
      <template v-else-if="modalType === 'audit'">
        <ManagePurchaseOrderAuditForm
          :order-id="currentRecord.id"
          @success="handleAuditSuccess"
          @cancel="handleCancel"
        />
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { PlusOutlined, ReloadOutlined } from "@ant-design/icons-vue";
  import {
    reactive,
    ref,
    onMounted,
    computed,
    defineAsyncComponent,
  } from "vue";
  import { message } from "ant-design-vue";
  import dayjs from "dayjs";
  import type { TableInstance } from "~/components/manage/base/table.client.vue";

  // 暂时注释掉组件导入，待组件文件创建好后再取消注释
  // import OrderDetail from "~/components/purchase/OrderDetail.vue";
  // import OrderForm from "~/components/purchase/OrderForm.vue";
  // import ReceiveForm from "~/components/purchase/ReceiveForm.vue";

  // 导入审核表单组件
  const ManagePurchaseOrderDetail = defineAsyncComponent(
    () => import("~/components/manage/purchase/OrderDetail.vue")
  );
  const ManagePurchaseOrderForm = defineAsyncComponent(
    () => import("~/components/manage/purchase/OrderForm.vue")
  );
  const ManagePurchaseOrderReceiveForm = defineAsyncComponent(
    () => import("~/components/manage/purchase/ReceiveForm.vue")
  );
  const ManagePurchaseOrderAuditForm = defineAsyncComponent(
    () => import("~/components/manage/purchase/AuditForm.vue")
  );

  // 定义订单状态选项
  const orderStatus = [
    { label: "草稿", value: "draft" },
    { label: "待审核", value: "pending" },
    { label: "已审核", value: "approved" },
    { label: "已拒绝", value: "rejected" },
    { label: "部分收货", value: "partially_received" },
    { label: "完成", value: "completed" },
    { label: "取消", value: "cancelled" },
  ];

  // 搜索表单数据
  const searchForm = reactive({
    orderNo: "",
    supplierName: "",
    status: undefined as string | undefined,
    dateRange: undefined as [string, string] | undefined,
  });

  // 表格相关数据
  const loading = ref(false);
  const tableData = ref<
    Array<{
      id: number;
      orderNo: string;
      supplierName: string;
      totalAmount: number;
      createdBy: string;
      createdAt: string;
      expectedDeliveryDate: string | null;
      status: string;
      [key: string]: any;
    }>
  >([]);
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total: number) => `共 ${total} 条`,
  });

  // 表格列定义
  const columns: ExtendedTableColumnProps<any>[] = [
    { title: "订单编号", dataIndex: "orderNo", key: "orderNo" },
    {
      title: "供应商",
      dataIndex: "supplierName",
      key: "supplierName",
      customRender: ({ record }: { record: any }) => {
        return record.supplier.name || "无";
      },
    },
    { title: "订单金额", dataIndex: "totalAmount", key: "totalAmount" },
    {
      title: "创建人",
      dataIndex: "createdBy",
      key: "createdBy",
      customRender: ({ record }: { record: any }) => {
        return record.user.username || "无";
      },
    },
    {
      title: "预计到货日期",
      dataIndex: "expectedDeliveryDate",
      key: "expectedDeliveryDate",
    },
    { title: "状态", dataIndex: "status", key: "status" },
    { title: "操作", key: "action" },
  ];

  // 弹窗相关
  const modalOpen = ref(false);
  const modalTitle = ref("新增采购订单");
  const modalType = ref<string>("form");
  const currentRecord = ref<any>({});

  const query = useApiTrpc().admin.purchase.queryPurchaseOrders.query;
  const tableRef = ref<TableInstance>();
  // 获取订单列表数据
  const fetchData = async () => {
    loading.value = true;
    try {
      // 准备查询参数
      const queryParams = {
        orderNo: searchForm.orderNo || undefined,
        supplierName: searchForm.supplierName || undefined,
        status: searchForm.status || undefined,
        startDate: searchForm.dateRange?.[0] || undefined,
        endDate: searchForm.dateRange?.[1] || undefined,
        skip: (pagination.current - 1) * pagination.pageSize,
        take: pagination.pageSize,
      };

      // 调用API获取数据
      const result =
        await useApiTrpc().admin.purchase.queryPurchaseOrders.query(
          queryParams
        );

      if (result.code === 200) {
        // 根据后端API实际返回结构调整
        const data = result.data;
        tableData.value = data.result || [];
        pagination.total = data.total || 0;
      } else {
        message.error(result.message || "获取数据失败");
      }
    } catch (error) {
      console.error("获取数据失败", error);
      message.error("获取数据失败，请稍后重试");
    } finally {
      loading.value = false;
    }
  };

  // 根据状态值获取对应的文本
  const getStatusText = (status: string) => {
    const item = orderStatus.find((item) => item.value === status);
    return item ? item.label : "未知状态";
  };

  // 根据状态获取标签颜色
  const getStatusColor = (status: string) => {
    const statusColorMap: Record<string, string> = {
      draft: "default",
      pending: "processing",
      approved: "success",
      rejected: "error",
      partially_received: "warning",
      completed: "success",
      cancelled: "default",
    };
    return statusColorMap[status] || "default";
  };

  // 事件处理函数
  const handleSearch = () => {
    pagination.current = 1;
    // fetchData();
    tableRef.value?.query();
  };

  const handleAdd = () => {
    modalTitle.value = "新增采购订单";
    modalType.value = "form";
    currentRecord.value = {};
    modalOpen.value = true;
  };

  const handleView = (record: any) => {
    modalTitle.value = "查看采购订单";
    modalType.value = "view";
    currentRecord.value = { ...record };
    modalOpen.value = true;
  };

  const handleEdit = (record: any) => {
    modalTitle.value = "编辑采购订单";
    modalType.value = "form";
    currentRecord.value = { ...record };
    modalOpen.value = true;
  };

  const handleDelete = async (record: any) => {
    try {
      const result =
        await useApiTrpc().admin.purchase.deletePurchaseOrder.mutate({
          id: record.id,
        });

      if (result.code === 200) {
        message.success("删除订单成功");
        tableRef.value?.query();
      } else {
        message.error(result.message || "删除订单失败");
      }
    } catch (error) {
      console.error("删除订单失败", error);
      message.error("删除订单失败，请稍后重试");
    }
  };

  const handleSubmit = async (record: any) => {
    try {
      const result =
        await useApiTrpc().admin.purchase.submitPurchaseOrder.mutate({
          id: record.id,
        });

      if (result.code === 200) {
        message.success("提交审核成功");
        tableRef.value?.query();
      } else {
        message.error(result.message || "提交审核失败");
      }
    } catch (error) {
      console.error("提交审核失败", error);
      message.error("提交审核失败，请稍后重试");
    }
  };

  const handleReceive = (record: any) => {
    modalTitle.value = "收货记录";
    modalType.value = "receive";
    currentRecord.value = { ...record };
    modalOpen.value = true;
  };

  const handleFormSuccess = () => {
    modalOpen.value = false;
    tableRef.value?.query();
    message.success(currentRecord.value.id ? "更新订单成功" : "创建订单成功");
  };

  const handleReceiveSuccess = () => {
    modalOpen.value = false;
    tableRef.value?.query();
    message.success("收货成功");
  };

  const handleCancel = () => {
    modalOpen.value = false;
  };

  const handleRefresh = () => {
    tableRef.value?.query();
  };

  const resetSearch = () => {
    searchForm.orderNo = "";
    searchForm.supplierName = "";
    searchForm.status = undefined;
    searchForm.dateRange = undefined;
    handleSearch();
  };

  // 审核订单处理函数
  const handleAudit = (record: any) => {
    modalTitle.value = "审核采购订单";
    modalType.value = "audit";
    currentRecord.value = { ...record };
    modalOpen.value = true;
  };

  // 审核成功回调
  const handleAuditSuccess = (auditResult: {
    approved: boolean;
    comments: string;
  }) => {
    modalOpen.value = false;
    tableRef.value?.query();
    message.success(auditResult.approved ? "订单审核通过" : "订单已被拒绝");
  };
</script>

<style scoped>
  /* .purchase-order-container {
    padding: 0 10px;
  } */

  .table-operations {
    margin-bottom: 16px;
    margin-top: 16px;
  }

  .danger-text {
    color: #ff4d4f;
  }

  :deep(.ant-table-pagination.ant-pagination) {
    margin: 16px 0;
  }
</style>
