<template>
  <div class="auth-management">
    <a-card title="权限管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>新增权限
          </a-button>
        </a-space>
      </template>

      <!-- 使用系统封装的表格组件 -->
      <manage-base-table
        ref="tableRef"
        :columns="columns"
        :model="searchForm"
        :query="queryAuthList"
        rowKey="id"
        bordered
      >
        <template #searchBox>
          <a-form-item name="name" label="权限名称">
            <manage-base-search-input
              v-model:value="searchForm.name"
              placeholder="请输入权限名称"
            />
          </a-form-item>
          <a-form-item name="code" label="权限代码">
            <a-input
              v-model:value="searchForm.code"
              placeholder="请输入权限代码"
            />
          </a-form-item>
          <a-form-item name="status" label="状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              style="width: 150px"
              allowClear
            >
              <a-select-option value="active">启用</a-select-option>
              <a-select-option value="inactive">禁用</a-select-option>
            </a-select>
          </a-form-item>
        </template>

        <!-- 表格单元格自定义渲染 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="record.status === 'active' ? 'success' : 'error'">
              {{ record.status === "active" ? "启用" : "禁用" }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-popconfirm
                title="确定要删除这个权限吗？"
                @confirm="handleDelete(record)"
              >
                <a>删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </manage-base-table>
    </a-card>

    <!-- 新增/编辑权限弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :maskClosable="false"
      :closable="true"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :rules="rules"
        :model="formState"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="权限名称" name="name">
          <a-input
            v-model:value="formState.name"
            placeholder="请输入权限名称"
          />
        </a-form-item>
        <a-form-item label="权限代码" name="code">
          <a-input
            v-model:value="formState.code"
            placeholder="请输入权限代码"
          />
        </a-form-item>
        <a-form-item label="权限描述" name="description">
          <a-textarea
            v-model:value="formState.description"
            placeholder="请输入权限描述"
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formState.status">
            <a-radio value="active">启用</a-radio>
            <a-radio value="inactive">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="saveLoading">
          保存
        </a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from "vue";
  import { PlusOutlined } from "@ant-design/icons-vue";
  import { message } from "ant-design-vue";
  import type { FormInstance } from "ant-design-vue/es/form";
  import type { TableInstance } from "~/components/manage/base/table.client.vue";
  import { z } from "zod";

  // 权限表单验证 schema
  const authSchema = z.object({
    id: z.number().optional(),
    name: z
      .string()
      .min(2, "权限名称至少2个字符")
      .max(50, "权限名称最多50个字符"),
    code: z
      .string()
      .min(2, "权限代码至少2个字符")
      .max(50, "权限代码最多50个字符"),
    description: z.string().max(200, "权限描述最多200个字符").optional(),
    status: z.enum(["active", "inactive"]),
  });

  // API调用
  const createAuth = useApiTrpc().admin.auth.createAuth.mutate;
  const updateAuth = useApiTrpc().admin.auth.updateAuth.mutate;
  const deleteAuth = useApiTrpc().admin.auth.deleteAuth.mutate;
  const queryAuthList = useApiTrpc().admin.auth.getAuthList.query;

  type AuthSchema = z.infer<typeof authSchema>;

  const tableRef = ref<TableInstance>();
  const formRef = ref<FormInstance>();
  const saveLoading = ref(false);
  const modalVisible = ref(false);
  const modalTitle = ref("新增权限");

  // 搜索表单数据
  const searchForm = reactive({
    name: undefined as string | undefined,
    code: undefined as string | undefined,
    status: undefined as string | undefined,
  });

  // 表单数据
  const formState = reactive<AuthSchema>({
    name: "",
    code: "",
    description: "",
    status: "active",
  });

  // 表格列定义
  const columns = [
    { title: "权限名称", dataIndex: "name", key: "name" },
    { title: "权限代码", dataIndex: "code", key: "code" },
    { title: "描述", dataIndex: "description", key: "description" },
    { title: "状态", dataIndex: "status", key: "status" },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      defaultVisible: false,
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      align: "center",
    },
  ];

  // 表单验证规则
  const rules = {
    name: [{ required: true, message: "请输入权限名称" }],
    code: [{ required: true, message: "请输入权限代码" }],
    status: [{ required: true, message: "请选择状态" }],
  };

  // 处理新增
  const handleAdd = () => {
    modalTitle.value = "新增权限";
    formState.id = undefined;
    formState.name = "";
    formState.code = "";
    formState.description = "";
    formState.status = "active";
    modalVisible.value = true;
  };

  // 处理编辑
  const handleEdit = (record: AuthSchema) => {
    modalTitle.value = "编辑权限";
    Object.assign(formState, record);
    modalVisible.value = true;
  };

  // 处理删除
  const handleDelete = async (record: AuthSchema) => {
    try {
      await deleteAuth({ id: record.id! });
      message.success("删除成功");
      tableRef.value?.refresh();
    } catch (error) {
      message.error("删除失败");
    }
  };

  // 处理取消
  const handleCancel = () => {
    modalVisible.value = false;
    formRef.value?.resetFields();
  };

  // 处理提交
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();
      saveLoading.value = true;

      if (formState.id) {
        await updateAuth(formState);
        message.success("更新成功");
      } else {
        await createAuth(formState);
        message.success("创建成功");
      }

      modalVisible.value = false;
      tableRef.value?.refresh();
    } catch (error) {
      message.error("保存失败");
    } finally {
      saveLoading.value = false;
    }
  };
</script>

<style scoped>
  .auth-management {
    padding: 24px;
  }
</style>
