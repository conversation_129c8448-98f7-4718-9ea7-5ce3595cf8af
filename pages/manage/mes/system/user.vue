<template>
  <div class="user-management">
    <a-card title="用户管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>新增用户
          </a-button>
        </a-space>
      </template>

      <!-- 使用系统封装的表格组件 -->
      <manage-base-table
        ref="tableRef"
        :columns="columns"
        :model="searchForm"
        :query="queryUserList"
        quickSearchPlaceholder="输入用户名或姓名查询"
        quickSearch
        rowKey="id"
        bordered
      >
        <template #searchBox>
          <a-form-item name="username" label="用户名">
            <manage-base-search-input
              v-model:value="searchForm.username"
              placeholder="请输入用户名"
            />
          </a-form-item>
          <a-form-item name="email" label="邮箱">
            <a-input
              v-model:value="searchForm.email"
              placeholder="请输入邮箱"
            />
          </a-form-item>
          <a-form-item name="role" label="角色">
            <manage-user-role-selector v-model:value="searchForm.role_id" />
          </a-form-item>
          <a-form-item name="status" label="状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              style="width: 150px"
              allowClear
            >
              <a-select-option value="active">启用</a-select-option>
              <a-select-option value="inactive">禁用</a-select-option>
            </a-select>
          </a-form-item>
        </template>

        <!-- 表格单元格自定义渲染 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="record.status === 'active' ? 'success' : 'error'">
              {{ record.status === "active" ? "启用" : "禁用" }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a @click="handleViewPhoto(record)">查看照片</a>
            </a-space>
          </template>
        </template>
      </manage-base-table>
    </a-card>

    <!-- 新增/编辑用户弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :maskClosable="false"
      :closable="true"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :rules="rules"
        :model="formState"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="用户名" name="username">
          <a-input
            v-model:value="formState.username"
            placeholder="请输入用户名"
          />
        </a-form-item>
        <a-form-item label="姓名" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入姓名" />
        </a-form-item>
        <a-form-item label="邮箱" name="email">
          <a-input v-model:value="formState.email" placeholder="请输入邮箱" />
        </a-form-item>
        <a-form-item
          :label="formState.id ? '密码(不修改留空)' : '密码'"
          name="password"
        >
          <a-input-password
            v-model:value="formState.password"
            placeholder="请输入密码"
            autocomplete="new-password"
          />
        </a-form-item>
        <a-form-item label="角色" name="role_id">
          <manage-user-role-selector v-model:value="formState.role_id" />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formState.status">
            <a-radio value="active">启用</a-radio>
            <a-radio value="inactive">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="人脸识别" v-if="formState.id">
          <a-button type="primary" @click="handleFaceCapture">
            <template #icon><CameraOutlined /></template>
            采集人脸特征
          </a-button>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="saveLoading">
          保存
        </a-button>
      </template>
    </a-modal>

    <!-- 人脸采集弹窗 -->
    <a-modal
      v-model:open="faceModalVisible"
      title="人脸特征采集"
      :maskClosable="false"
      :closable="true"
      @cancel="handleFaceCaptureCancel"
      width="800px"
    >
      <div class="face-capture-container">
        <div class="video-container">
          <video
            ref="videoRef"
            autoplay
            muted
            playsinline
            :class="{ 'camera-ready': isCameraReady }"
          ></video>
          <canvas ref="canvasRef" class="face-canvas"></canvas>
        </div>

        <div class="tips">
          <p>请确保:</p>
          <ul>
            <li>面部在摄像头范围内</li>
            <li>光线充足</li>
            <li>正面面对摄像头</li>
          </ul>
        </div>
      </div>
      <template #footer>
        <a-button @click="handleFaceCaptureCancel">取消</a-button>
        <a-button
          type="primary"
          :loading="isLoading"
          :disabled="!isCameraReady"
          @click="captureFace"
        >
          {{ isLoading ? "采集中..." : "采集人脸特征" }}
        </a-button>
      </template>
    </a-modal>
    <a-modal v-model:open="photoModalVisible" title="人脸照片" width="800px">
      <img :src="faceFeature.faceImage" alt="人脸照片" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted } from "vue";
  import { PlusOutlined, CameraOutlined } from "@ant-design/icons-vue";
  import { message, notification } from "ant-design-vue";
  import type { FormInstance } from "ant-design-vue/es/form";
  import type { Rule } from "ant-design-vue/es/form";
  import type { TableInstance } from "~/components/manage/base/table.client.vue";
  import { z } from "zod";
  import { Form } from "ant-design-vue";
  import * as faceapi from "face-api.js";

  // 用户表单验证 schema
  const userSchema = z.object({
    id: z.number(),
    name: z.string(),
    username: z
      .string()
      .min(6, "用户名至少6个字符")
      .max(20, "用户名最多20个字符"),
    password: z.string().min(6, "密码至少6个字符"),
    email: z.string().email("请输入有效的邮箱地址").optional(),
    role_id: z.number().min(1, "请选择用户角色"),
    status: z.enum(["active", "inactive"]),
  });
  // 创建用户
  const createUser = useApiTrpc().admin.user.createUser.mutate;
  type CreateUserParams = z.infer<typeof userSchema>;
  // 更新用户
  const updateUser = useApiTrpc().admin.user.updateUser.mutate;
  type UpdateUserParams = z.infer<typeof userSchema>;

  // const tableRef = ref<TableInstance>();
  const saveLoading = ref(false);

  // 搜索表单数据
  const searchForm = reactive({
    quickSearch: undefined as string | undefined,
    username: undefined as string | undefined,
    email: undefined as string | undefined,
    role_id: undefined as number | undefined,
    status: undefined as string | undefined,
  });

  // 表格列定义
  const columns: ExtendedTableColumnProps<any>[] = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80,
      defaultVisible: false,
    },
    { title: "用户名", dataIndex: "username", key: "username" },
    { title: "姓名", dataIndex: "name", key: "name" },
    { title: "邮箱", dataIndex: "email", key: "email" },
    { title: "角色", dataIndex: ["role", "name"], key: "role" },
    { title: "状态", dataIndex: "status", key: "status" },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      defaultVisible: false,
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      align: "center",
    },
  ];

  // 弹窗相关
  const modalVisible = ref(false);
  const modalTitle = ref("新增用户");
  const formState = reactive({
    id: undefined as number | undefined,
    username: "",
    name: "",
    email: undefined as string | undefined,
    password: "",
    role_id: 0,
    status: "active" as "active" | "inactive",
  });
  // const formRef = ref<FormInstance>();
  const formRef = useTemplateRef("formRef");
  const tableRef = useTemplateRef("tableRef");
  // const formRef = Form.useForm(formState, zodObjectToAntRules(userSchema));
  const rules = zodObjectToAntRules(userSchema);
  const queryUserList = useApiTrpc().admin.user.queryUser.query;
  // 处理编辑用户
  const handleEdit = (record: UpdateUserParams) => {
    modalTitle.value = "编辑用户";
    formState.id = record.id;
    formState.name = record.name;
    formState.username = record.username;
    formState.email = record.email;
    formState.password = record.password;
    formState.role_id = record.role_id;
    formState.status = record.status;
    modalVisible.value = true;
  };

  // 处理添加用户
  const handleAdd = () => {
    modalTitle.value = "新增用户";
    formState.id = undefined;
    formState.username = "";
    formState.name = "";
    formState.email = undefined;
    formState.password = "";
    formState.role_id = 0;
    formState.status = "active";
    modalVisible.value = true;
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      saveLoading.value = true;
      // await formRef.value?.validate();
      if (formState.id) {
        const validatedData = userSchema.parse(formState);
        await updateUser(validatedData);
      } else {
        const validatedData = userSchema.omit({ id: true }).parse(formState);
        await createUser(validatedData);
      }

      message.success(formState.id ? "更新成功" : "添加成功");
      modalVisible.value = false;
      tableRef.value?.query();
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.log(error.errors);
        error.errors.forEach((error) => {
          message.error(error.message);
        });
      } else if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error("操作失败");
      }
    } finally {
      saveLoading.value = false;
    }
  };

  // 处理模态框取消
  const handleCancel = () => {
    modalVisible.value = false;
    //@ts-ignore
    formRef.value?.resetFields();
  };

  // 人脸采集相关状态
  const faceModalVisible = ref(false);
  const videoRef = ref<HTMLVideoElement | null>(null);
  const canvasRef = ref<HTMLCanvasElement | null>(null);
  const isLoading = ref(false);
  const isCameraReady = ref(false);
  const stream = ref<MediaStream | null>(null);

  // 初始化face-api模型
  const initFaceApi = async () => {
    try {
      await faceapi.nets.tinyFaceDetector.loadFromUri("/models");
      await faceapi.nets.faceLandmark68Net.loadFromUri("/models");
      await faceapi.nets.faceRecognitionNet.loadFromUri("/models");
    } catch (error) {
      message.error("人脸识别模型加载失败");
      console.error("Failed to load face-api models:", error);
    }
  };

  // 启动摄像头
  const startCamera = async () => {
    try {
      stream.value = await navigator.mediaDevices.getUserMedia({
        video: {
          width: 640,
          height: 480,
        },
      });

      if (videoRef.value) {
        videoRef.value.srcObject = stream.value;
        isCameraReady.value = true;
      }
    } catch (error) {
      message.error("无法访问摄像头");
      console.error("Failed to access camera:", error);
    }
  };

  // 停止摄像头
  const stopCamera = () => {
    if (stream.value) {
      stream.value.getTracks().forEach((track) => track.stop());
      stream.value = null;
      isCameraReady.value = false;
    }
  };

  // 采集人脸特征
  const captureFace = async () => {
    if (!videoRef.value || !canvasRef.value || isLoading.value) return;

    isLoading.value = true;
    try {
      const detections = await faceapi
        .detectSingleFace(videoRef.value, new faceapi.TinyFaceDetectorOptions())
        .withFaceLandmarks()
        .withFaceDescriptor();

      if (!detections) {
        message.warning("未检测到人脸，请确保面部在摄像头范围内");
        isLoading.value = false;
        return;
      }

      // 检查人脸大小
      const { width, height } = detections.detection.box;
      const minSize = 100; // 最小像素要求
      if (width < minSize || height < minSize) {
        message.warning("人脸图像太小，请靠近摄像头");
        isLoading.value = false;
        return;
      }

      // 获取人脸图像的base64数据
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");
      if (!context) {
        throw new Error("无法创建canvas上下文");
      }

      canvas.width = videoRef.value.videoWidth;
      canvas.height = videoRef.value.videoHeight;
      context.drawImage(videoRef.value, 0, 0, canvas.width, canvas.height);

      const box = detections.detection.box;
      const faceCanvas = document.createElement("canvas");
      const faceContext = faceCanvas.getContext("2d");
      if (!faceContext) {
        throw new Error("无法创建人脸canvas上下文");
      }

      const padding = 50;
      faceCanvas.width = box.width + padding * 2;
      faceCanvas.height = box.height + padding * 2;

      faceContext.drawImage(
        canvas,
        box.x - padding,
        box.y - padding,
        box.width + padding * 2,
        box.height + padding * 2,
        0,
        0,
        faceCanvas.width,
        faceCanvas.height
      );

      // 检查图片质量
      const faceImageBase64 = faceCanvas.toDataURL("image/jpeg", 0.95); // 提高图片质量
      const img = new Image();
      img.src = faceImageBase64;
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
      });

      // 检查图片尺寸
      if (img.width < 100 || img.height < 100) {
        message.warning("图片尺寸太小，请调整摄像头位置");
        isLoading.value = false;
        return;
      }

      try {
        // 保存人脸特征数据
        await useApiTrpc().admin.user.saveFaceFeature.mutate({
          userId: formState.id!,
          username: formState.username,
          faceDescriptor: Array.from(detections.descriptor),
          faceImage: faceImageBase64,
        });

        message.success("人脸特征采集成功");
        stopCamera();
        faceModalVisible.value = false;
      } catch (error: any) {
        if (error.message.includes("image check fail")) {
          message.error(
            "图片质量检查失败，请确保：\n1. 光线充足\n2. 人脸正面朝向摄像头\n3. 面部特征清晰可见"
          );
        } else {
          message.error(error.message || "人脸特征采集失败");
        }
        console.error("Capture face error:", error);
      }
    } catch (error) {
      message.error("人脸特征采集失败");
      console.error("Capture face error:", error);
    } finally {
      isLoading.value = false;
    }
  };

  // 打开人脸采集弹窗
  const handleFaceCapture = async () => {
    if (!formState.id) {
      message.warning("请先保存用户信息");
      return;
    }

    faceModalVisible.value = true;
    await initFaceApi();
    await startCamera();
  };

  // 关闭人脸采集弹窗
  const handleFaceCaptureCancel = () => {
    stopCamera();
    faceModalVisible.value = false;
  };
  const photoModalVisible = ref(false);
  const faceFeature = ref<any>(null);
  // 查看照片
  const handleViewPhoto = async (record: UpdateUserParams) => {
    const { data } = await useApiTrpc().admin.user.getFaceFeature.query({
      userId: record.id,
    });
    if (data) {
      faceFeature.value = data;
      photoModalVisible.value = true;
    } else {
      message.warning("未采集人脸特征");
    }
  };

  onMounted(() => {
    // 初始化页面时加载数据
    // tableRef.value?.query();
    console.log(zodObjectToAntRules(userSchema));
  });

  onUnmounted(() => {
    stopCamera();
  });
</script>

<style scoped>
  /* .user-management {
    padding: 24px;
  } */

  .danger-text {
    color: #ff4d4f;
  }

  .face-capture-container {
    .video-container {
      position: relative;
      width: 100%;
      max-width: 640px;
      margin: 0 auto;
      background-color: #000;
      border-radius: 8px;
      overflow: hidden;

      video {
        width: 100%;
        height: auto;
        transform: scaleX(-1);
      }

      .face-canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }

      .camera-ready {
        border: 2px solid #52c41a;
      }
    }

    .tips {
      margin-top: 20px;
      padding: 16px;
      background-color: #f6f6f6;
      border-radius: 4px;

      ul {
        margin: 8px 0 0 20px;
        padding: 0;
      }

      li {
        margin-bottom: 4px;
        color: #666;
      }
    }
  }
</style>
