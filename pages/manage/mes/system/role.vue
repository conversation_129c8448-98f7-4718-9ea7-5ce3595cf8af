<template>
  <div class="role-management">
    <a-card title="角色管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>新增角色
          </a-button>
        </a-space>
      </template>

      <!-- 使用系统封装的表格组件 -->
      <manage-base-table
        ref="tableRef"
        :columns="columns"
        :model="searchForm"
        :query="queryRoleList"
        rowKey="id"
        bordered
      >
        <template #searchBox>
          <a-form-item name="name" label="角色名称">
            <manage-base-search-input
              v-model:value="searchForm.name"
              placeholder="请输入角色名称"
            />
          </a-form-item>
          <a-form-item name="status" label="状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              style="width: 150px"
              allowClear
            >
              <a-select-option value="active">启用</a-select-option>
              <a-select-option value="inactive">禁用</a-select-option>
            </a-select>
          </a-form-item>
        </template>

        <!-- 表格单元格自定义渲染 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="record.status === 'active' ? 'success' : 'error'">
              {{ record.status === "active" ? "启用" : "禁用" }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a @click="handleMenu(record)">菜单</a>
              <a-popconfirm
                title="确定要删除这个角色吗？"
                @confirm="handleDelete(record)"
              >
                <a class="danger-text">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </manage-base-table>
    </a-card>

    <!-- 新增/编辑角色弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :maskClosable="false"
      :closable="true"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :rules="rules"
        :model="formState"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="角色名称" name="name">
          <a-input
            v-model:value="formState.name"
            placeholder="请输入角色名称"
          />
        </a-form-item>
        <a-form-item label="角色描述" name="description">
          <a-textarea
            v-model:value="formState.description"
            placeholder="请输入角色描述"
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formState.status">
            <a-radio value="1">启用</a-radio>
            <a-radio value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="saveLoading">
          保存
        </a-button>
      </template>
    </a-modal>

    <!-- 菜单管理弹窗 -->
    <a-modal
      v-model:open="menuModalVisible"
      :title="menuModalTitle"
      :maskClosable="false"
      :closable="true"
      @cancel="handleMenuCancel"
    >
      <div class="menu-tree-container">
        <a-tree
          v-model:checkedKeys="selectedMenus"
          :tree-data="menuTreeData"
          checkable
          :check-strictly="false"
          :defaultExpandAll="true"
          :field-names="{
            title: 'title',
            key: 'name',
            children: 'items',
          }"
          @check="handleCheck"
        />
      </div>
      <template #footer>
        <a-button @click="handleMenuCancel">取消</a-button>
        <a-button
          type="primary"
          @click="handleMenuSubmit"
          :loading="menuSaveLoading"
        >
          保存
        </a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from "vue";
  import { PlusOutlined } from "@ant-design/icons-vue";
  import { message } from "ant-design-vue";
  import type { FormInstance } from "ant-design-vue/es/form";
  import type { TableInstance } from "~/components/manage/base/table.client.vue";
  import { z } from "zod";
  import { menuItems } from "~/utils/manage-menu";
  import type { MenuItem } from "~/utils/manage-menu";
  import type { TreeDataItem } from "ant-design-vue/es/tree";

  // 角色表单验证 schema
  const roleSchema = z.object({
    id: z.number(),
    name: z
      .string()
      .min(2, "角色名称至少2个字符")
      .max(50, "角色名称最多50个字符"),
    description: z.string().max(200, "角色描述最多200个字符").optional(),
    status: z.enum(["active", "inactive"]),
    code: z.string(),
    Permission: z.any(),
    Menu: z.any(),
  });

  const createRoleSchema = roleSchema.omit({ id: true });

  const tableRef = ref<TableInstance>();
  const saveLoading = ref(false);

  // 搜索表单数据
  const searchForm = reactive({
    name: undefined as string | undefined,
    status: undefined as string | undefined,
  });

  // 表格列定义
  const columns: ExtendedTableColumnProps<any>[] = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80,
      defaultVisible: false,
    },
    { title: "角色名称", dataIndex: "name", key: "name" },
    { title: "角色描述", dataIndex: "description", key: "description" },
    { title: "状态", dataIndex: "status", key: "status" },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      defaultVisible: false,
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      align: "center" as const,
    },
  ];

  // 弹窗相关
  const modalVisible = ref(false);
  const modalTitle = ref("新增角色");
  const formState = reactive({
    id: undefined as number | undefined,
    name: "",
    description: "",
    status: "active" as "active" | "inactive",
    code: "",
    Permission: {},
    Menu: {},
  });

  const menuModalVisible = ref(false);
  const menuModalTitle = ref("分配菜单");
  const menuFormState = reactive({
    id: undefined as number | undefined,
    name: "",
    description: "",
  });

  // 添加菜单树相关变量
  const menuTreeData = ref<TreeDataItem[]>(
    menuItems.map((item) => ({
      ...item,
      key: item.name,
      children: item.items?.map((subItem) => ({
        ...subItem,
        key: subItem.name,
      })),
    }))
  );

  const selectedMenus = ref<string[]>([]);
  const halfCheckedKeys = ref<string[]>([]);
  const handleCheck = (checkedKeys: string[], e: any) => {
    halfCheckedKeys.value = [...e.halfCheckedKeys];
  };

  const handleMenuCancel = () => {
    menuModalVisible.value = false;
  };

  // 修改菜单提交函数
  const handleMenuSubmit = async () => {
    try {
      menuSaveLoading.value = true;
      await useApiTrpc().admin.user.updateRoleMenus.mutate({
        roleId: menuFormState.id!,
        menuNames: selectedMenus.value,
      });

      message.success("菜单权限更新成功");
      menuModalVisible.value = false;
    } catch (error) {
      if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error("更新菜单权限失败");
      }
    } finally {
      menuSaveLoading.value = false;
    }
  };

  const menuSaveLoading = ref(false);

  const formRef = ref<FormInstance>();
  const rules = zodObjectToAntRules(roleSchema);

  // API 调用
  const queryRoleList = useApiTrpc().admin.user.queryRole.query;
  const createRole = useApiTrpc().admin.user.createRole.mutate;
  const updateRole = useApiTrpc().admin.user.updateRole.mutate;
  const deleteRole = useApiTrpc().admin.user.deleteRole.mutate;

  // 修改菜单处理函数
  const handleMenu = async (record: any) => {
    menuModalVisible.value = true;
    menuModalTitle.value = `分配菜单 - ${record.name}`;
    menuFormState.id = record.id;
    menuFormState.name = record.name;
    menuFormState.description = record.description;

    try {
      // 获取当前角色已分配的菜单
      const response = await useApiTrpc().admin.user.getRoleMenus.query({
        roleId: record.id,
      });
      console.log(response);
      selectedMenus.value = response.data.Menu.map((item: string) => item);
    } catch (error) {
      if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error("获取角色菜单失败");
      }
    }
  };

  // 处理编辑角色
  const handleEdit = (record: any) => {
    modalTitle.value = "编辑角色";
    Object.assign(formState, record);
    modalVisible.value = true;
  };

  // 处理添加角色
  const handleAdd = () => {
    modalTitle.value = "新增角色";
    formState.id = undefined;
    formState.name = "";
    formState.description = "";
    formState.status = "active";
    formState.code = "";
    formState.Permission = {};
    formState.Menu = {};
    modalVisible.value = true;
  };

  // 处理删除角色
  const handleDelete = async (record: any) => {
    try {
      await deleteRole({ id: record.id });
      message.success("删除成功");
      tableRef.value?.query();
    } catch (error) {
      if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error("删除失败");
      }
    }
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      saveLoading.value = true;
      await formRef.value?.validate();

      if (formState.id) {
        const validatedData = roleSchema.parse({
          ...formState,
          id: formState.id,
        });
        await updateRole(validatedData);
      } else {
        const validatedData = createRoleSchema.parse(formState);
        await createRole(validatedData);
      }

      message.success(formState.id ? "更新成功" : "添加成功");
      modalVisible.value = false;
      tableRef.value?.query();
    } catch (error) {
      if (error instanceof z.ZodError) {
        error.errors.forEach((error) => {
          message.error(error.message);
        });
      } else if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error("操作失败");
      }
    } finally {
      saveLoading.value = false;
    }
  };

  // 处理模态框取消
  const handleCancel = () => {
    modalVisible.value = false;
    formRef.value?.resetFields();
  };
</script>

<style scoped>
  .danger-text {
    color: #ff4d4f;
  }

  .menu-tree-container {
    max-height: 600px;
    overflow-y: auto;
    padding: 12px;
  }
</style>
