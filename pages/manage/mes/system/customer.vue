<template>
  <div class="customer-container">
    <a-card title="客户管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>新增客户
          </a-button>
        </a-space>
      </template>
      <!-- 表格 -->
      <manage-base-table
        ref="tableRef"
        :columns="columns"
        :model="searchForm"
        :query="query"
        rowKey="id"
        bordered
        :scroll="{ x: 1500 }"
      >
        <template #searchBox>
          <a-form-item name="name" label="联系人">
            <manage-base-search-input
              v-model:value="searchForm.name"
              placeholder="请输入联系人姓名"
            />
          </a-form-item>
          <a-form-item name="comname" label="公司名称">
            <a-input
              v-model:value="searchForm.comname"
              placeholder="请输入公司名称"
            />
          </a-form-item>
          <a-form-item name="mobile" label="手机号码">
            <a-input
              v-model:value="searchForm.mobile"
              placeholder="请输入手机号码"
            />
          </a-form-item>
          <a-form-item name="lock" label="状态">
            <a-select
              v-model:value="searchForm.lock"
              placeholder="请选择状态"
              style="width: 120px"
              allowClear
            >
              <a-select-option :value="false">正常</a-select-option>
              <a-select-option :value="true">锁定</a-select-option>
            </a-select>
          </a-form-item>
        </template>

        <!-- 自定义列渲染 -->
        <template #bodyCell="{ column, record }">
          <!-- 状态列 -->
          <template v-if="column.dataIndex === 'lock'">
            <a-tag :color="record.lock ? 'red' : 'green'">
              {{ record.lock ? "锁定" : "正常" }}
            </a-tag>
          </template>

          <!-- 收货信息和发票信息列 -->
          <template v-if="column.dataIndex === 'receivingCount'">
            {{ record._count?.ReceivingInfo || 0 }}
          </template>
          <template v-if="column.dataIndex === 'invoiceCount'">
            {{ record._count?.InvoiceInfo || 0 }}
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" @click="handleView(record)">查看</a-button>
              <a-button type="link" @click="handleEdit(record)">编辑</a-button>
              <a-button
                type="link"
                :danger="!record.lock"
                @click="handleToggleLock(record)"
              >
                {{ record.lock ? "解锁" : "锁定" }}
              </a-button>
            </a-space>
          </template>
        </template>
      </manage-base-table>
    </a-card>

    <!-- 客户详情抽屉 -->
    <a-drawer
      v-model:open="drawerVisible"
      title="客户详情"
      width="800"
      :footer-style="{ textAlign: 'right' }"
      @close="drawerVisible = false"
    >
      <template #extra>
        <a-button @click="drawerVisible = false">关闭</a-button>
      </template>

      <a-descriptions title="基本信息" bordered>
        <a-descriptions-item label="联系人" :span="3">
          {{ currentCustomer?.name }}
        </a-descriptions-item>
        <a-descriptions-item label="公司名称" :span="3">
          {{ currentCustomer?.comname }}
        </a-descriptions-item>
        <a-descriptions-item label="手机号码">
          {{ currentCustomer?.mobile }}
        </a-descriptions-item>
        <a-descriptions-item label="电话">
          {{ currentCustomer?.tel || "-" }}
        </a-descriptions-item>
        <a-descriptions-item label="邮箱">
          {{ currentCustomer?.email || "-" }}
        </a-descriptions-item>
        <a-descriptions-item label="地址" :span="3">
          {{ currentCustomer?.address }}
        </a-descriptions-item>
        <a-descriptions-item label="传真" :span="3">
          {{ currentCustomer?.fox || "-" }}
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="currentCustomer?.lock ? 'red' : 'green'">
            {{ currentCustomer?.lock ? "锁定" : "正常" }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建人">
          {{ currentCustomer?.Admin?.name }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ formatDate(currentCustomer?.createAt) }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 收货信息 -->
      <a-divider orientation="left">收货信息</a-divider>
      <a-button
        type="primary"
        style="margin-bottom: 16px"
        @click="handleAddReceivingInfo"
        v-if="!currentCustomer?.lock"
      >
        <template #icon><PlusOutlined /></template>添加收货信息
      </a-button>

      <a-table
        :dataSource="currentCustomer?.ReceivingInfo || []"
        :columns="receivingColumns"
        rowKey="id"
        size="small"
        bordered
        :scroll="{ x: 500 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button
                type="link"
                @click="
                  handleEditReceivingInfo(record as unknown as ReceivingInfo)
                "
                v-if="!currentCustomer?.lock"
              >
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除该收货信息吗？"
                @confirm="
                  handleDeleteReceivingInfo(record as unknown as ReceivingInfo)
                "
                ok-text="确定"
                cancel-text="取消"
                v-if="!currentCustomer?.lock"
              >
                <a-button type="link" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 发票信息 -->
      <a-divider orientation="left">发票信息</a-divider>
      <a-button
        type="primary"
        style="margin-bottom: 16px"
        @click="handleAddInvoiceInfo"
        v-if="!currentCustomer?.lock"
      >
        <template #icon><PlusOutlined /></template>添加发票信息
      </a-button>

      <a-table
        :dataSource="currentCustomer?.InvoiceInfo || []"
        :columns="invoiceColumns"
        rowKey="id"
        size="small"
        bordered
        :scroll="{ x: 500 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'invoiceType'">
            {{ formatInvoiceType(record.invoiceType) }}
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button
                type="link"
                @click="handleEditInvoiceInfo(record as unknown as InvoiceInfo)"
                v-if="!currentCustomer?.lock"
              >
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除该发票信息吗？"
                @confirm="
                  handleDeleteInvoiceInfo(record as unknown as InvoiceInfo)
                "
                ok-text="确定"
                cancel-text="取消"
                v-if="!currentCustomer?.lock"
              >
                <a-button type="link" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-drawer>

    <!-- 客户表单组件 -->
    <CustomerFormAdd ref="addFormRef" @save-success="handleSaveSuccess" />

    <CustomerFormEdit ref="editFormRef" @save-success="handleSaveSuccess" />

    <!-- 收货信息表单组件 -->
    <CustomerFormReceivingInfo
      ref="receivingFormRef"
      @save-success="handleSaveSuccess"
    />

    <!-- 发票信息表单组件 -->
    <CustomerFormInvoiceInfo
      ref="invoiceFormRef"
      @save-success="handleSaveSuccess"
    />
  </div>
</template>

<script setup lang="ts">
  import { PlusOutlined } from "@ant-design/icons-vue";
  import { message } from "ant-design-vue";
  import dayjs from "dayjs";

  // 导入客户表单组件
  import CustomerFormAdd from "~/components/customer/form/add.client.vue";
  import CustomerFormEdit from "~/components/customer/form/edit.client.vue";
  import CustomerFormReceivingInfo from "~/components/customer/form/receiving-info.client.vue";
  import CustomerFormInvoiceInfo from "~/components/customer/form/invoice-info.client.vue";

  // 定义类型
  interface Customer {
    id: number;
    name: string;
    comname: string;
    mobile: string;
    tel?: string;
    email?: string;
    address: string;
    fox?: string;
    lock: boolean;
    createAt: string;
    updateAt: string;
    user_id: number;
    Admin?: {
      name: string;
    };
    _count?: {
      ReceivingInfo: number;
      InvoiceInfo: number;
    };
    ReceivingInfo?: ReceivingInfo[];
    InvoiceInfo?: InvoiceInfo[];
  }

  interface ReceivingInfo {
    id: number;
    customer_id: number;
    recName: string;
    recAddress: string;
    recTel: string;
    createAt: string;
    updateAt: string;
    user_id: number;
  }

  interface InvoiceInfo {
    id: number;
    customer_id: number;
    title: string;
    taxNumber: string;
    bankName?: string;
    bankAccount?: string;
    address?: string;
    tel?: string;
    invoiceType: "common" | "special" | "electronic";
    createAt: string;
    updateAt: string;
  }

  // 表格列定义
  const columns: any = [
    {
      title: "ID",
      dataIndex: "id",
      width: 60,
    },
    {
      title: "联系人",
      dataIndex: "name",
      width: 100,
    },
    {
      title: "公司名称",
      dataIndex: "comname",
      width: 180,
      ellipsis: true,
    },
    {
      title: "手机号码",
      dataIndex: "mobile",
      width: 120,
    },
    {
      title: "电话",
      dataIndex: "tel",
      width: 100,
    },
    {
      title: "地址",
      dataIndex: "address",
      ellipsis: true,
      width: 150,
    },
    {
      title: "收货信息",
      dataIndex: "receivingCount",
      width: 80,
    },
    {
      title: "发票信息",
      dataIndex: "invoiceCount",
      width: 80,
    },
    {
      title: "状态",
      dataIndex: "lock",
      width: 70,
    },
    {
      title: "创建人",
      dataIndex: ["Admin", "name"],
      width: 80,
    },
    {
      title: "创建时间",
      dataIndex: "createAt",
      width: 160,
      customRender: ({ text }: { text: string }) => {
        return text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss") : "-";
      },
    },
    {
      title: "操作",
      dataIndex: "action",
      fixed: "right",
      width: 200,
    },
  ];

  // 收货信息表格列
  const receivingColumns = [
    {
      title: "收货人",
      dataIndex: "recName",
      width: 120,
    },
    {
      title: "收货地址",
      dataIndex: "recAddress",
      width: 200,
    },
    {
      title: "联系电话",
      dataIndex: "recTel",
      width: 120,
    },
    {
      title: "操作",
      dataIndex: "action",
      width: 150,
    },
  ];

  // 发票信息表格列
  const invoiceColumns = [
    {
      title: "发票抬头",
      dataIndex: "title",
      width: 200,
    },
    {
      title: "税号",
      dataIndex: "taxNumber",
      width: 180,
    },
    {
      title: "发票类型",
      dataIndex: "invoiceType",
      width: 100,
    },
    {
      title: "操作",
      dataIndex: "action",
      width: 150,
    },
  ];

  // 搜索表单
  const searchForm = reactive({
    name: "",
    comname: "",
    mobile: "",
    lock: undefined,
  });

  // 表格引用
  const tableRef = ref();

  // 抽屉状态
  const drawerVisible = ref(false);
  const currentCustomer = ref<Customer | null>(null);

  // 表单引用
  const addFormRef = ref();
  const editFormRef = ref();
  const receivingFormRef = ref();
  const invoiceFormRef = ref();

  // TRPC接口引用
  const trpc = useApiTrpc();
  const query = trpc.admin.customer.queryCustomer.query;
  const getCustomerQuery = trpc.admin.customer.getCustomer.query;
  const toggleCustomerLockMutation =
    trpc.admin.customer.toggleCustomerLock.mutate;
  const deleteCustomerReceivingInfoMutation =
    trpc.admin.customer.deleteCustomerReceivingInfo.mutate;
  const deleteCustomerInvoiceInfoMutation =
    trpc.admin.customer.deleteCustomerInvoiceInfo.mutate;

  // 新增客户
  const handleAdd = () => {
    addFormRef.value.show();
  };

  // 编辑客户
  const handleEdit = (record: Customer) => {
    editFormRef.value.show(record);
  };

  // 查看客户详情
  const handleView = async (record: Customer) => {
    try {
      const { data } = await getCustomerQuery({ id: record.id });
      currentCustomer.value = data;
      drawerVisible.value = true;
    } catch (error) {
      message.error("获取客户详情失败");
      console.error(error);
    }
  };

  // 锁定/解锁客户
  const handleToggleLock = async (record: Customer) => {
    try {
      await toggleCustomerLockMutation({
        id: record.id,
        lock: !record.lock,
      });
      message.success(record.lock ? "客户已解锁" : "客户已锁定");
      refreshTable();
    } catch (error) {
      message.error("操作失败");
      console.error(error);
    }
  };

  // 添加收货信息
  const handleAddReceivingInfo = () => {
    if (currentCustomer.value) {
      receivingFormRef.value.show({
        customer_id: currentCustomer.value.id,
      });
    }
  };

  // 编辑收货信息
  const handleEditReceivingInfo = (record: ReceivingInfo) => {
    receivingFormRef.value.show(record);
  };

  // 删除收货信息
  const handleDeleteReceivingInfo = async (record: ReceivingInfo) => {
    try {
      await deleteCustomerReceivingInfoMutation({ id: record.id });
      message.success("删除收货信息成功");
      refreshCustomerDetail();
    } catch (error) {
      message.error("删除收货信息失败");
      console.error(error);
    }
  };

  // 添加发票信息
  const handleAddInvoiceInfo = () => {
    if (currentCustomer.value) {
      invoiceFormRef.value.show({
        customer_id: currentCustomer.value.id,
      });
    }
  };

  // 编辑发票信息
  const handleEditInvoiceInfo = (record: InvoiceInfo) => {
    invoiceFormRef.value.show(record);
  };

  // 删除发票信息
  const handleDeleteInvoiceInfo = async (record: InvoiceInfo) => {
    try {
      await deleteCustomerInvoiceInfoMutation({ id: record.id });
      message.success("删除发票信息成功");
      refreshCustomerDetail();
    } catch (error) {
      message.error("删除发票信息失败");
      console.error(error);
    }
  };

  // 保存成功回调
  const handleSaveSuccess = () => {
    refreshTable();
    refreshCustomerDetail();
  };

  // 刷新表格
  const refreshTable = () => {
    if (tableRef.value) {
      tableRef.value.query();
    }
  };

  // 刷新客户详情
  const refreshCustomerDetail = async () => {
    if (currentCustomer.value && drawerVisible.value) {
      try {
        const { data } = await getCustomerQuery({
          id: currentCustomer.value.id,
        });
        currentCustomer.value = data as Customer;
      } catch (error) {
        console.error(error);
      }
    }
  };

  // 格式化日期
  const formatDate = (date: string | undefined) => {
    return date ? dayjs(date).format("YYYY-MM-DD HH:mm:ss") : "-";
  };

  // 格式化发票类型
  const formatInvoiceType = (type: "common" | "special" | "electronic") => {
    const types = {
      common: "普通发票",
      special: "专用发票",
      electronic: "电子发票",
    };
    return types[type] || type;
  };
</script>

<style scoped>
  .customer-container {
    padding: 0;
  }
</style>
