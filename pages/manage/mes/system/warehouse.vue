<template>
  <a-card title="仓库管理">
    <template #extra>
      <a-button @click="handleAddWarehouse"> <plus-outlined /> 添加 </a-button>
    </template>
    <manage-base-table
      :columns="columns"
      :query="queryWarehouseList"
      ref="tableRef"
      rowKey="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'type'">
          {{ record.type }}
        </template>
        <template v-if="column.key === 'action'">
          <a-button type="link" @click="handleEditWarehouse(record)"
            >编辑</a-button
          >
        </template>
      </template>
    </manage-base-table>
  </a-card>
  <a-modal v-model:open="modalOpen" title="添加仓库" @ok="save()">
    <a-form
      ref="formRef"
      :model="formState"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="仓库名称" name="name">
        <a-input v-model:value="formState.name" />
      </a-form-item>
      <a-form-item label="仓库地址" name="address">
        <a-input v-model:value="formState.address" />
      </a-form-item>
      <a-form-item label="仓库类型" name="type">
        <a-select v-model:value="formState.type">
          <a-select-option value="waste">废料库</a-select-option>
          <a-select-option value="sales">销售库</a-select-option>
          <a-select-option value="production">生产库</a-select-option>
          <a-select-option value="finished">成品库</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="锁定" name="lock">
        <a-switch v-model:checked="formState.lock" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
  import { WarehouseType } from "@prisma/client";
  const formRef = ref();
  const modalOpen = ref(false);
  const formState = reactive({
    id: 0,
    name: "",
    address: "",
    lock: false,
    type: "production" as WarehouseType,
  });
  const editFlag = ref(false);

  const queryWarehouseList =
    useApiTrpc().admin.warehouse.queryWarehouseList.query;
  const addWarehouse = useApiTrpc().admin.warehouse.addWarehouse.mutate;
  const updateWarehouse = useApiTrpc().admin.warehouse.updateWarehouse.mutate;
  const handleAddWarehouse = () => {
    console.log("添加仓库");
    modalOpen.value = true;
    editFlag.value = false;
  };
  const handleEditWarehouse = (record: any) => {
    editFlag.value = true;
    const data = deepClone(record);
    formState.id = data.id;
    formState.name = data.name;
    formState.address = data.address;
    formState.lock = data.lock;
    modalOpen.value = true;
  };
  const save = async () => {
    console.log("保存仓库", formState);
    if (editFlag.value) {
      // await useApiFetch.updateWarehouse(formState);
      updateWarehouse(formState);
    } else {
      // await useApiFetch.addWarehouse(formState);
      addWarehouse(formState);
    }
    formRef.value.resetFields();
    modalOpen.value = false;
    tableRef.value.query();
  };
  const columns = [
    { title: "仓库名称", dataIndex: "name", key: "name" },
    { title: "仓库地址", dataIndex: "address", key: "address" },
    { title: "锁定", dataIndex: "lock", key: "lock" },
    { title: "仓库类型", dataIndex: "type", key: "type" },
    { title: "操作", key: "action" },
  ];
  const tableRef = ref();
</script>
