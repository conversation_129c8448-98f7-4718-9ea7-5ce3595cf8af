<template>
  <a-card title="生产入库">
    <!-- 表格区域 -->
    <manage-base-table
      :columns="columns"
      :model="searchForm"
      :query="queryFn"
      ref="tableRef"
    >
      <template #searchBox>
        <a-form-item label="工单编号">
          <a-input
            v-model:value="searchForm.taskCode"
            placeholder="请输入工单编号"
            allow-clear
          ></a-input>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="resetSearch">重置</a-button>
          </a-space>
        </a-form-item>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-button
            type="primary"
            @click="handleProductionIn(record)"
            :disabled="record.production_task?.status !== 2"
          >
            入库
          </a-button>
        </template>
      </template>
    </manage-base-table>

    <!-- 入库模态框 -->
    <a-modal
      v-model:open="productionInModalOpen"
      title="生产入库"
      @ok="handleProductionInSubmit"
      :confirmLoading="productionInModalLoading"
      width="800px"
    >
      <a-descriptions
        :title="`工单编号: ${productionInForm.taskNo}`"
        :column="3"
        bordered
      >
        <a-descriptions-item label="工单编号">
          {{ productionInForm.taskNo }}
        </a-descriptions-item>
        <a-descriptions-item label="产品名称">
          {{ productionInForm.productName }}
        </a-descriptions-item>
        <a-descriptions-item label="计划数量">
          {{ productionInForm.planQuantity }}
        </a-descriptions-item>
        <a-descriptions-item label="已完成数量">
          {{ productionInForm.completed_quantity }}
        </a-descriptions-item>
        <a-descriptions-item label="合格品数量">
          {{ productionInForm.qualified_quantity }}
        </a-descriptions-item>
        <a-descriptions-item label="不合格品数量">
          {{ productionInForm.unqualified_quantity }}
        </a-descriptions-item>
        <a-descriptions-item label="生产员" :span="3">
          <a-tag
            v-for="worker in productionInForm.workers"
            :key="worker.id"
            color="blue"
            style="margin-right: 8px"
          >
            {{ worker.name }}
          </a-tag>
          <span
            v-if="
              !productionInForm.workers || productionInForm.workers.length === 0
            "
          >
            暂无分配生产员
          </span>
        </a-descriptions-item>
      </a-descriptions>
      <a-divider>主产品入库信息</a-divider>

      <a-form :model="productionInForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="入库仓库"
              name="warehouse_id"
              :rules="[{ required: true, message: '请选择入库仓库' }]"
            >
              <manage-warehouse-selector
                :type="['production', 'finished', 'sales']"
                v-model:value="productionInForm.warehouse_id"
              ></manage-warehouse-selector>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="批次号"
              name="batch_no"
              :rules="[{ required: true, message: '请输入批次号' }]"
            >
              <a-input
                v-model:value="productionInForm.batch_no"
                placeholder="请输入批次号"
              ></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="实际入库数量"
              name="qualified_quantity"
              :rules="[
                { required: true, message: '请输入实际入库数量' },
                { type: 'number', min: 1, message: '数量必须大于0' },
              ]"
            >
              <a-input-number
                v-model:value="productionInForm.qualified_quantity"
                :min="1"
                style="width: 100%"
                @change="handleQualifiedQuantityChange"
              ></a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="备注" name="note">
              <a-textarea
                v-model:value="productionInForm.note"
                placeholder="请输入备注信息"
                :rows="2"
              ></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- 不合格品入库信息 -->
      <template v-if="productionInForm.unqualified_quantity > 0">
        <a-divider>不合格品入库信息</a-divider>
        <a-form :model="productionInForm" layout="vertical">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="废品库" name="waste_warehouse_id">
                <manage-warehouse-selector
                  :type="['waste']"
                  v-model:value="productionInForm.waste_warehouse_id"
                ></manage-warehouse-selector>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="废品批次号" name="waste_batch_no">
                <a-input
                  v-model:value="productionInForm.waste_batch_no"
                  placeholder="请输入废品批次号"
                ></a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="废品入库数量" name="waste_quantity">
                <a-input-number
                  v-model:value="productionInForm.waste_quantity"
                  :min="0"
                  :max="productionInForm.unqualified_quantity"
                  style="width: 100%"
                ></a-input-number>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-alert
                v-if="
                  productionInForm.waste_warehouse_id &&
                  productionInForm.waste_quantity > 0
                "
                message="将同时入库不合格品到废品库"
                type="info"
                show-icon
              />
            </a-col>
          </a-row>
        </a-form>
      </template>

      <!-- 副产物信息 -->
      <template v-if="byproducts.length > 0">
        <a-divider>副产物信息</a-divider>
        <a-table
          :dataSource="byproducts"
          :columns="byproductColumns"
          size="small"
          :pagination="false"
          bordered
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'warehouse_id'">
              <manage-warehouse-selector
                style="width: 100%"
                :type="['waste', 'production', 'finished']"
                v-model:value="record.warehouse_id"
              ></manage-warehouse-selector>
            </template>
            <template v-if="column.key === 'batch_no'">
              <a-input
                v-model:value="record.batch_no"
                placeholder="请输入批次号"
              />
            </template>
            <template v-if="column.key === 'calculatedQuantity'">
              {{ calculateByproductQuantity(record) }}
            </template>
            <template v-if="column.key === 'actualQuantity'">
              <a-input-number
                v-model:value="record.quantity"
                :min="0"
                style="width: 100%"
              />
            </template>
          </template>
        </a-table>
      </template>
    </a-modal>

    <!-- 确认入库对话框 -->
    <a-modal
      v-model:open="confirmModalOpen"
      title="确认入库信息"
      :footer="null"
      width="450px"
    >
      <div>
        <p><strong>工单编号：</strong>{{ productionInForm.taskNo }}</p>
        <p><strong>产品名称：</strong>{{ productionInForm.productName }}</p>
        <p>
          <strong>入库数量：</strong>{{ productionInForm.qualified_quantity }}
        </p>
        <p><strong>批次号：</strong>{{ productionInForm.batch_no }}</p>
        <p style="margin-top: 16px; font-weight: bold; color: #1890ff">
          确认要执行入库操作吗？
        </p>
        <p style="color: #666; font-size: 12px">系统将自动判断是否完成工单</p>
      </div>
      <div style="margin-top: 20px; text-align: right">
        <a-button
          style="margin-right: 8px"
          @click="handleConfirmAction('cancel')"
        >
          取消
        </a-button>
        <a-button type="primary" @click="handleConfirmAction('confirm')">
          确认入库
        </a-button>
      </div>
    </a-modal>
  </a-card>
</template>

<script lang="ts" setup>
  import { ref, reactive, h } from "vue";
  import { message, Modal } from "ant-design-vue";
  import dayjs from "dayjs";

  // 表格列定义 - 显示生产报工记录
  const columns = [
    {
      title: "工单编号",
      key: "taskCode",
      customRender: ({ record }: { record: any }) => {
        return record.production_task?.code || "-";
      },
    },
    {
      title: "产品名称",
      key: "productName",
      customRender: ({ record }: { record: any }) => {
        return record.production_task?.Materiel?.name || "-";
      },
    },
    {
      title: "报工时间",
      dataIndex: "createAt",
      key: "createAt",
      customRender: ({ text }: { text: any }) => {
        return text ? dayjs(text).format("YYYY-MM-DD HH:mm") : "-";
      },
    },
    {
      title: "报工员",
      key: "reporter",
      customRender: ({ record }: { record: any }) => {
        return record.user?.name || "-";
      },
    },
    {
      title: "合格品数量",
      dataIndex: "quantity",
      key: "quantity",
      customRender: ({ text }: { text: any }) => {
        return Number(text) || 0;
      },
    },
    {
      title: "不合格品数量",
      dataIndex: "unqualified_quantity",
      key: "unqualified_quantity",
      customRender: ({ text }: { text: any }) => {
        return Number(text) || 0;
      },
    },
    {
      title: "生产员",
      key: "workers",
      customRender: ({ record }: { record: any }) => {
        if (
          record.production_task?.users &&
          record.production_task.users.length > 0
        ) {
          return record.production_task.users
            .map((u: any) => u.user?.name || "")
            .join(", ");
        }
        return "-";
      },
    },
    { title: "操作", key: "action" },
  ];

  // 副产物表格列定义
  const byproductColumns = [
    { title: "副产物名称", dataIndex: "materiel_name", key: "materiel_name" },
    {
      title: "配方比例",
      dataIndex: "ratio",
      key: "ratio",
      customRender: ({ text }: { text: any }) => {
        return Number(text).toFixed(2);
      },
    },
    {
      title: "计算数量",
      key: "calculatedQuantity",
    },
    { title: "实际数量", key: "actualQuantity" },
    { title: "入库仓库", key: "warehouse_id" },
    { title: "批次号", key: "batch_no" },
  ];

  // 搜索表单
  const searchForm = reactive({
    taskCode: "",
  });

  // 入库表单
  const productionInForm = reactive({
    id: null as number | null,
    reportId: null as number | null, // 报工记录ID
    taskNo: "",
    productName: "",
    planQuantity: 0,
    completed_quantity: 0,
    qualified_quantity: 0,
    unqualified_quantity: 0,
    materiel_id: null as number | null,
    warehouse_id: undefined as number | undefined,
    quantity: 0,
    batch_no: "",
    note: "",
    // 废品入库相关字段
    waste_warehouse_id: undefined as number | undefined,
    waste_quantity: 0,
    waste_batch_no: "",
    // 生产员信息
    workers: [] as any[],
  });

  // 副产物数据
  const byproducts = ref<any[]>([]);
  const taskByproducts = ref<any[]>([]);

  // 模态框状态
  const productionInModalOpen = ref(false);
  const productionInModalLoading = ref(false);
  const confirmModalOpen = ref(false);

  const tableRef = ref();

  // 仓库选项
  const warehouseOptions = ref<any[]>([]);

  // 查询函数 - 查询生产报工记录
  const queryFn = async (params: any) => {
    try {
      const queryParams = {
        taskCode: searchForm.taskCode,
        ...params,
      };

      const response =
        await useApiTrpc().admin.production.queryProductionReportsForInbound.query(
          queryParams
        );

      return response;
    } catch (error) {
      console.error("查询生产报工记录失败:", error);
      message.error("查询生产报工记录失败");
      return { data: { result: [], total: 0 } };
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: number) => {
    switch (status) {
      case 0:
        return "default";
      case 1:
        return "processing";
      case 2:
        return "processing";
      case 3:
        return "success";
      case 4:
        return "error";
      default:
        return "default";
    }
  };

  // 获取状态文本
  const getStatusText = (status: number) => {
    switch (status) {
      case 0:
        return "草稿";
      case 1:
        return "待领料";
      case 2:
        return "生产中";
      case 3:
        return "已完成";
      case 4:
        return "已终止";
      default:
        return "未知状态";
    }
  };

  // 处理搜索
  const handleSearch = () => {
    tableRef.value?.refresh();
  };

  // 重置搜索
  const resetSearch = () => {
    searchForm.taskCode = "";
    handleSearch();
  };

  // 获取仓库列表
  const fetchWarehouses = async () => {
    try {
      const result =
        await useApiTrpc().admin.warehouse.queryWarehouseList.query({});
      if (result && result.data && result.data.result) {
        warehouseOptions.value = result.data.result;
      }
    } catch (error) {
      console.error("获取仓库列表失败", error);
      message.error("获取仓库列表失败");
    }
  };

  // 获取工单的副产物信息
  const fetchByproducts = async (materialId: number) => {
    try {
      // 从生产配方中获取副产物信息
      const result = await useApiTrpc().admin.bom.querySubBom.query({
        materielId: materialId,
      });

      if (result && result.data) {
        taskByproducts.value = (result.data.result || []).map((item) => ({
          byproduct_material_id: item.childId,
          byproduct_material_name: item.child?.name || "",
          ratio: item.quantity || 0,
        }));

        // 初始化副产物数据
        byproducts.value = taskByproducts.value.map((item) => {
          return {
            materiel_id: item.byproduct_material_id,
            materiel_name: item.byproduct_material_name,
            ratio: item.ratio,
            quantity: calculateProductQuantity(
              item.ratio,
              productionInForm.qualified_quantity
            ),
            warehouse_id: productionInForm.warehouse_id,
            batch_no: productionInForm.batch_no,
          };
        });
      }
    } catch (error) {
      console.error("获取副产物信息失败", error);
      message.error("获取副产物信息失败");
    }
  };

  // 计算副产物数量
  const calculateProductQuantity = (ratio: number, mainQuantity: number) => {
    return Math.round(ratio * mainQuantity * 100) / 100; // 保留两位小数并四舍五入
  };

  // 当主产品数量变化时，重新计算副产物数量
  const handleQualifiedQuantityChange = (value: number | string) => {
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(numValue)) return;

    byproducts.value = byproducts.value.map((item) => {
      const byproductInfo = taskByproducts.value.find(
        (bp) => bp.byproduct_material_id === item.materiel_id
      );

      if (byproductInfo) {
        return {
          ...item,
          quantity: calculateProductQuantity(byproductInfo.ratio, numValue),
        };
      }
      return item;
    });
  };

  // 在表格中计算并显示副产物数量
  const calculateByproductQuantity = (record: any) => {
    const byproductInfo = taskByproducts.value.find(
      (bp) => bp.byproduct_material_id === record.materiel_id
    );

    if (byproductInfo) {
      return calculateProductQuantity(
        byproductInfo.ratio,
        productionInForm.qualified_quantity
      );
    }
    return 0;
  };

  // 处理生产入库 - 基于报工记录
  const handleProductionIn = async (record: any) => {
    try {
      // record 现在是报工记录，包含 production_task 信息
      const task = record.production_task;

      if (!task) {
        message.error("工单信息不完整");
        return;
      }

      // 使用当前报工记录的数据
      const qualifiedQuantity = Number(record.quantity) || 0;
      const unqualifiedQuantity = Number(record.unqualified_quantity) || 0;
      const completedQuantity = qualifiedQuantity + unqualifiedQuantity;

      productionInForm.id = task.id; // 工单ID
      productionInForm.reportId = record.id; // 报工记录ID
      productionInForm.taskNo = task.code;
      productionInForm.productName = task.Materiel?.name || "";
      productionInForm.planQuantity = Number(task.quantity) || 0;
      productionInForm.completed_quantity = completedQuantity;
      productionInForm.qualified_quantity = qualifiedQuantity;
      productionInForm.unqualified_quantity = unqualifiedQuantity;
      productionInForm.materiel_id = task.materiel_id;
      productionInForm.warehouse_id = undefined;

      // 设置生产员信息
      productionInForm.workers = task.users
        ? task.users.map((u: any) => ({
            id: u.user?.id,
            code: u.user?.code || "",
            name: u.user?.name || "",
          }))
        : [];

      // 生成批次号
      // const batchPrefix = dayjs().format("YYYYMMDD");
      // const batchSuffix = Math.floor(Math.random() * 1000)
      //   .toString()
      //   .padStart(3, "0");
      const batchPrefix = dayjs(record.createAt).format("YYYYMMDD");
      console.log(productionInForm.workers);
      const batchSuffix = productionInForm.workers[0].code || "000";
      const batchNo = `${batchPrefix}-${batchSuffix}`;

      productionInForm.batch_no = batchNo;
      productionInForm.quantity = qualifiedQuantity; // 默认入库合格品数量
      productionInForm.note = record.note || ""; // 使用报工记录的备注

      // 废品入库相关字段初始化
      productionInForm.waste_warehouse_id = undefined;
      productionInForm.waste_quantity = unqualifiedQuantity;
      productionInForm.waste_batch_no = `W-${batchNo}`; // 废品批次号前缀为W-

      await fetchWarehouses();

      // 获取副产物信息
      if (task.materiel_id) {
        await fetchByproducts(task.materiel_id);
      }

      productionInModalOpen.value = true;
    } catch (error) {
      console.error("获取生产入库数据失败:", error);
      message.error("获取生产入库数据失败");
    }
  };

  // 处理入库提交
  const handleProductionInSubmit = async () => {
    try {
      productionInModalLoading.value = true;

      // 表单验证
      if (!productionInForm.warehouse_id) {
        message.error("请选择入库仓库");
        productionInModalLoading.value = false;
        return;
      }

      if (!productionInForm.batch_no) {
        message.error("请输入批次号");
        productionInModalLoading.value = false;
        return;
      }

      if (
        !productionInForm.qualified_quantity ||
        productionInForm.qualified_quantity <= 0
      ) {
        message.error("请输入有效的入库数量");
        productionInModalLoading.value = false;
        return;
      }

      // 验证废品入库信息
      if (
        productionInForm.unqualified_quantity > 0 &&
        productionInForm.waste_quantity > 0 &&
        productionInForm.waste_warehouse_id
      ) {
        if (!productionInForm.waste_batch_no) {
          message.error("请输入废品批次号");
          productionInModalLoading.value = false;
          return;
        }

        if (
          productionInForm.waste_quantity >
          productionInForm.unqualified_quantity
        ) {
          message.error("废品入库数量不能大于不合格品数量");
          productionInModalLoading.value = false;
          return;
        }
      }

      // 验证副产物
      let isValid = true;
      byproducts.value.forEach((item, index) => {
        if (!item.warehouse_id) {
          message.error(`请为第${index + 1}个副产物选择入库仓库`);
          isValid = false;
          return;
        }

        if (!item.batch_no) {
          message.error(`请为第${index + 1}个副产物输入批次号`);
          isValid = false;
          return;
        }
      });

      if (!isValid) {
        productionInModalLoading.value = false;
        return;
      }

      // 显示确认对话框进行二次确认
      productionInModalLoading.value = false;
      showConfirmDialog();
    } catch (error: any) {
      console.error("表单验证失败", error);
      message.error(error?.message || "表单验证失败");
      productionInModalLoading.value = false;
    }
  };

  // 显示确认对话框
  const showConfirmDialog = () => {
    confirmModalOpen.value = true;
  };

  // 处理确认对话框的操作
  const handleConfirmAction = (action: "cancel" | "confirm") => {
    confirmModalOpen.value = false;

    if (action === "cancel") {
      return;
    }

    // 执行入库操作
    performProductionIn();
  };

  // 执行生产入库操作
  const performProductionIn = async () => {
    try {
      productionInModalLoading.value = true;

      // 准备副产物数据
      const byproductData = byproducts.value.map((item) => ({
        materiel_id: item.materiel_id,
        quantity: item.quantity,
        warehouse_id: item.warehouse_id,
        batch_no: item.batch_no,
      }));

      // 使用新的统一入库API
      await useApiTrpc().admin.production.createProductionInRecord.mutate({
        productionTaskId: productionInForm.id!,
        productionReportId: productionInForm.reportId!,
        materiel_id: productionInForm.materiel_id!,
        quantity: productionInForm.qualified_quantity,
        batch_no: productionInForm.batch_no,
        note:
          productionInForm.note ||
          `生产工单${productionInForm.taskNo}的入库记录`,
        warehouse_id: productionInForm.warehouse_id!,
        waste_warehouse_id: productionInForm.waste_warehouse_id,
        waste_quantity: productionInForm.waste_quantity,
        waste_batch_no: productionInForm.waste_batch_no,
        byproducts: byproductData.length > 0 ? byproductData : undefined,
      });

      message.success("生产入库成功");
      productionInModalOpen.value = false;
      tableRef.value?.query();
    } catch (error: any) {
      console.error("生产入库失败", error);
      message.error(error?.message || "生产入库失败，请重试");
    } finally {
      productionInModalLoading.value = false;
    }
  };

  // 页面初始化时获取仓库列表
  onMounted(() => {
    fetchWarehouses();
  });
</script>
