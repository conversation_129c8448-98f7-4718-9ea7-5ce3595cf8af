<template>
  <a-card title="库存盘点">
    <template #extra>
      <a-space>
        <a-button
          type="primary"
          @click="handleCreateCheck"
          :disabled="!canCreateCheck"
        >
          <template #icon>
            <PlusOutlined />
          </template>
          新建盘点
        </a-button>
        <a-button
          type="default"
          @click="handleInitialStock"
          :disabled="!canInitialStock"
        >
          <template #icon>
            <PlusOutlined />
          </template>
          初始库存录入
        </a-button>
        <a-button @click="fetchData">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
      </a-space>
    </template>

    <!-- 搜索条件 -->
    <div class="search-form" style="margin-bottom: 16px">
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item label="盘点单号">
          <a-input
            v-model:value="searchForm.checkNo"
            placeholder="请输入盘点单号"
            allowClear
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allowClear
            style="width: 150px"
          >
            <a-select-option value="draft">草稿</a-select-option>
            <a-select-option value="checking">盘点中</a-select-option>
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="cancelled">已取消</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="仓库">
          <a-select
            v-model:value="searchForm.warehouseId"
            placeholder="请选择仓库"
            allowClear
            style="width: 200px"
          >
            <a-select-option
              v-for="warehouse in warehouseOptions"
              :key="warehouse.value"
              :value="warehouse.value"
            >
              {{ warehouse.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              查询
            </a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>

    <!-- 数据表格 -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      row-key="id"
      bordered
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <!-- 状态列 -->
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <!-- 盘点类型列 -->
        <template v-if="column.dataIndex === 'checkType'">
          <a-tag :color="record.checkType === 'full' ? 'blue' : 'orange'">
            {{ record.checkType === "full" ? "全盘" : "抽盘" }}
          </a-tag>
        </template>

        <!-- 进度列 -->
        <template v-if="column.dataIndex === 'progress'">
          <a-progress
            :percent="
              Math.round((record.checkedItems / record.totalItems) * 100)
            "
            size="small"
            :status="record.status === 'completed' ? 'success' : 'active'"
          />
          <span style="margin-left: 8px">
            {{ record.checkedItems }}/{{ record.totalItems }}
          </span>
        </template>

        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a-button
              type="link"
              size="small"
              @click="() => handleViewDetail(record)"
            >
              查看
            </a-button>
            <a-button
              v-if="record.status === 'draft' || record.status === 'checking'"
              type="link"
              size="small"
              @click="() => handleStartCheck(record)"
            >
              盘点
            </a-button>
            <a-button
              v-if="record.status === 'completed' && record.differenceItems > 0"
              type="link"
              size="small"
              @click="() => handleShowAdjustConfirm(record)"
            >
              调整库存
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 创建盘点单弹窗 -->
    <a-modal
      v-model:open="createModalVisible"
      title="新建盘点单"
      width="600px"
      @ok="handleCreateSubmit"
      @cancel="handleCreateCancel"
      :confirm-loading="createLoading"
    >
      <a-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        layout="vertical"
      >
        <a-form-item name="title" label="盘点标题">
          <a-input
            v-model:value="createForm.title"
            placeholder="请输入盘点标题"
          />
        </a-form-item>
        <a-form-item name="warehouse_id" label="盘点仓库">
          <a-select
            v-model:value="createForm.warehouse_id"
            placeholder="请选择仓库"
          >
            <a-select-option
              v-for="warehouse in warehouseOptions"
              :key="warehouse.value"
              :value="warehouse.value"
            >
              {{ warehouse.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="checkType" label="盘点类型">
          <a-radio-group v-model:value="createForm.checkType">
            <a-radio value="full">全盘</a-radio>
            <a-radio value="partial">抽盘</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item name="plannedDate" label="计划盘点日期">
          <a-date-picker
            v-model:value="createForm.plannedDate"
            style="width: 100%"
            placeholder="请选择计划盘点日期"
          />
        </a-form-item>
        <a-form-item name="note" label="备注">
          <a-textarea
            v-model:value="createForm.note"
            placeholder="请输入备注"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      :title="`盘点单详情 - ${currentStockCheck?.checkNo || ''}`"
      width="1200px"
      :footer="null"
      @cancel="handleDetailCancel"
    >
      <div v-if="currentStockCheck">
        <!-- 基本信息 -->
        <a-card title="基本信息" size="small" style="margin-bottom: 16px">
          <a-descriptions :column="3" bordered size="small">
            <a-descriptions-item label="盘点单号">
              {{ currentStockCheck.checkNo }}
            </a-descriptions-item>
            <a-descriptions-item label="盘点标题">
              {{ currentStockCheck.title }}
            </a-descriptions-item>
            <a-descriptions-item label="仓库">
              {{ currentStockCheck.warehouse?.name }}
            </a-descriptions-item>
            <a-descriptions-item label="盘点类型">
              <a-tag
                :color="
                  currentStockCheck.checkType === 'full' ? 'blue' : 'orange'
                "
              >
                {{ currentStockCheck.checkType === "full" ? "全盘" : "抽盘" }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag :color="getStatusColor(currentStockCheck.status)">
                {{ getStatusText(currentStockCheck.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="盘点进度">
              <a-progress
                :percent="
                  Math.round(
                    (currentStockCheck.checkedItems /
                      currentStockCheck.totalItems) *
                      100
                  )
                "
                size="small"
                :status="
                  currentStockCheck.status === 'completed'
                    ? 'success'
                    : 'active'
                "
              />
              <span style="margin-left: 8px">
                {{ currentStockCheck.checkedItems }}/{{
                  currentStockCheck.totalItems
                }}
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="计划日期">
              {{ formatDate(currentStockCheck.plannedDate) }}
            </a-descriptions-item>
            <a-descriptions-item label="实际日期">
              {{ formatDate(currentStockCheck.actualDate) || "-" }}
            </a-descriptions-item>
            <a-descriptions-item label="盘点人">
              {{ currentStockCheck.checkUser?.name }}
            </a-descriptions-item>
            <a-descriptions-item label="审核人">
              {{ currentStockCheck.approveUser?.name || "-" }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDateTime(currentStockCheck.createdAt) }}
            </a-descriptions-item>
            <a-descriptions-item label="备注" :span="2">
              {{ currentStockCheck.note || "-" }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 盘点明细 -->
        <a-card title="盘点明细" size="small">
          <template #extra>
            <a-space>
              <a-input-search
                v-model:value="detailSearchValue"
                placeholder="搜索物料名称或编码"
                style="width: 200px"
                allowClear
              />
              <a-button
                v-if="
                  currentStockCheck.status === 'draft' ||
                  currentStockCheck.status === 'checking'
                "
                type="primary"
                @click="handleOpenCheckModal"
              >
                开始盘点
              </a-button>
              <a-button
                v-if="
                  currentStockCheck.status === 'completed' &&
                  currentStockCheck.differenceItems > 0
                "
                type="primary"
                @click="handleShowAdjustConfirm(currentStockCheck)"
                :loading="adjustLoading"
              >
                调整库存
              </a-button>
            </a-space>
          </template>

          <a-table
            :columns="detailColumns"
            :data-source="filteredDetailItems"
            :loading="detailLoading"
            :pagination="{ pageSize: 10, showSizeChanger: false }"
            row-key="id"
            bordered
            size="small"
            :scroll="{ x: 1000 }"
          >
            <template #bodyCell="{ column, record }">
              <!-- 状态列 -->
              <template v-if="column.dataIndex === 'status'">
                <a-tag :color="getItemStatusColor(record.status)">
                  {{ getItemStatusText(record.status) }}
                </a-tag>
              </template>

              <!-- 差异数量列 -->
              <template v-if="column.dataIndex === 'differenceQuantity'">
                <span
                  :style="{
                    color: getDifferenceColor(record.differenceQuantity),
                    fontWeight: 'bold',
                  }"
                >
                  {{ formatQuantity(record.differenceQuantity) }}
                </span>
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </a-modal>

    <!-- 盘点执行弹窗 -->
    <a-modal
      v-model:open="checkModalVisible"
      :title="`执行盘点 - ${currentStockCheck?.checkNo || ''}`"
      width="1400px"
      :footer="null"
      @cancel="handleCheckCancel"
    >
      <div v-if="currentStockCheck">
        <!-- 盘点进度统计 -->
        <a-card size="small" style="margin-bottom: 16px">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic
                title="总盘点项数"
                :value="currentStockCheck.totalItems || 0"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="已盘点项数"
                :value="currentStockCheck.checkedItems || 0"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="差异项数"
                :value="currentStockCheck.differenceItems || 0"
                :value-style="{ color: '#ff4d4f' }"
              />
            </a-col>
            <a-col :span="6">
              <div style="text-align: center">
                <div style="margin-bottom: 8px; color: #666; font-size: 14px">
                  盘点进度
                </div>
                <a-progress
                  type="circle"
                  :percent="
                    Math.round(
                      (currentStockCheck.checkedItems /
                        currentStockCheck.totalItems) *
                        100
                    )
                  "
                  :size="60"
                  :status="
                    currentStockCheck.status === 'completed'
                      ? 'success'
                      : 'active'
                  "
                />
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 快速操作栏 -->
        <a-card size="small" style="margin-bottom: 16px">
          <a-row :gutter="16" align="middle">
            <a-col :span="8">
              <a-input-search
                v-model:value="checkSearchValue"
                placeholder="搜索物料名称、编码或批号"
                allowClear
              />
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="checkStatusFilter"
                placeholder="筛选状态"
                allowClear
              >
                <a-select-option value="pending">待盘点</a-select-option>
                <a-select-option value="checked">已盘点</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-checkbox v-model:checked="showDifferenceOnly">
                只显示有差异的
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <a-space>
                <a-button
                  @click="handleBatchCheck"
                  :disabled="selectedRowKeys.length === 0"
                >
                  批量盘点
                </a-button>
                <a-button
                  @click="handleMarkAllChecked"
                  :disabled="pendingCheckItems.length === 0"
                >
                  全部标记为无差异
                </a-button>
                <a-button
                  @click="handleSaveAllChanges"
                  :loading="saveLoading"
                  type="primary"
                >
                  保存所有更改
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-card>

        <!-- 盘点明细表格 -->
        <a-card title="盘点明细" size="small">
          <a-table
            :columns="checkColumns"
            :data-source="filteredCheckItems"
            :loading="checkLoading"
            :pagination="{ pageSize: 15, showSizeChanger: false }"
            :row-selection="checkRowSelection"
            row-key="id"
            bordered
            size="small"
            :scroll="{ x: 1200 }"
          >
            <template #bodyCell="{ column, record, index }">
              <!-- 序号列 -->
              <template v-if="column.dataIndex === 'index'">
                {{ index + 1 }}
              </template>

              <!-- 实盘数量列 -->
              <template v-if="column.dataIndex === 'actualQuantity'">
                <a-input-number
                  v-if="record.status === 'pending'"
                  v-model:value="record.actualQuantity"
                  :min="0"
                  :precision="4"
                  size="small"
                  style="width: 100%"
                  @change="handleQuantityChange(record)"
                  @pressEnter="handleNextItem(record, index)"
                />
                <span v-else>{{ formatQuantity(record.actualQuantity) }}</span>
              </template>

              <!-- 差异数量列 -->
              <template v-if="column.dataIndex === 'differenceQuantity'">
                <span
                  :style="{
                    color: getDifferenceColor(record.differenceQuantity),
                    fontWeight: 'bold',
                  }"
                >
                  {{ formatQuantity(record.differenceQuantity) }}
                </span>
              </template>

              <!-- 状态列 -->
              <template v-if="column.dataIndex === 'status'">
                <a-tag :color="getItemStatusColor(record.status)">
                  {{ getItemStatusText(record.status) }}
                </a-tag>
              </template>

              <!-- 操作列 -->
              <template v-if="column.dataIndex === 'action'">
                <a-space>
                  <a-button
                    v-if="record.status === 'pending'"
                    type="link"
                    size="small"
                    @click="handleConfirmItem(record)"
                    :disabled="
                      record.actualQuantity === null ||
                      record.actualQuantity === undefined
                    "
                  >
                    确认
                  </a-button>
                  <a-button
                    v-if="record.status === 'checked'"
                    type="link"
                    size="small"
                    @click="handleEditItem(record)"
                  >
                    修改
                  </a-button>
                  <a-popover title="备注" trigger="click" v-if="record.note">
                    <template #content>
                      <div style="max-width: 200px">{{ record.note }}</div>
                    </template>
                    <a-button type="link" size="small">备注</a-button>
                  </a-popover>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </a-modal>

    <!-- 批量盘点弹窗 -->
    <a-modal
      v-model:open="batchModalVisible"
      title="批量盘点"
      width="500px"
      @ok="handleBatchSubmit"
      @cancel="handleBatchCancel"
      :confirm-loading="batchLoading"
    >
      <a-alert
        message="批量操作提示"
        description="将为选中的所有项目设置相同的实盘数量等于系统数量（无差异）"
        type="info"
        style="margin-bottom: 16px"
      />
      <div>
        <p>
          已选择 <strong>{{ selectedRowKeys.length }}</strong> 个项目
        </p>
        <a-form layout="vertical">
          <a-form-item label="备注">
            <a-textarea
              v-model:value="batchNote"
              placeholder="请输入批量操作备注"
              :rows="3"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 库存调整确认弹窗 -->
    <a-modal
      v-model:open="adjustConfirmVisible"
      title="库存调整确认"
      width="800px"
      @ok="handleConfirmAdjust"
      @cancel="handleCancelAdjust"
      :confirm-loading="adjustLoading"
      :ok-button-props="{ disabled: !confirmAdjust }"
      ok-text="确认调整"
      cancel-text="取消"
    >
      <div v-if="adjustStockCheck">
        <a-alert
          message="重要提示"
          description="库存调整操作将直接修改系统库存数据，操作不可逆，请仔细确认后再执行！"
          type="warning"
          show-icon
          style="margin-bottom: 16px"
        />

        <!-- 盘点单基本信息 -->
        <a-descriptions
          title="盘点单信息"
          :column="2"
          bordered
          size="small"
          style="margin-bottom: 16px"
        >
          <a-descriptions-item label="盘点单号">
            {{ adjustStockCheck.checkNo }}
          </a-descriptions-item>
          <a-descriptions-item label="盘点标题">
            {{ adjustStockCheck.title }}
          </a-descriptions-item>
          <a-descriptions-item label="仓库">
            {{ adjustStockCheck.warehouse?.name }}
          </a-descriptions-item>
          <a-descriptions-item label="盘点类型">
            <a-tag
              :color="adjustStockCheck.checkType === 'full' ? 'blue' : 'orange'"
            >
              {{ adjustStockCheck.checkType === "full" ? "全盘" : "抽盘" }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="差异项数">
            <span style="color: #ff4d4f; font-weight: bold">
              {{ adjustStockCheck.differenceItems }} 项
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="盘点人">
            {{ adjustStockCheck.checkUser?.name }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 差异明细 -->
        <a-card title="差异明细" size="small">
          <a-table
            :columns="adjustColumns"
            :data-source="differenceItems"
            :pagination="false"
            row-key="id"
            bordered
            size="small"
            :scroll="{ y: 300 }"
          >
            <template #bodyCell="{ column, record }">
              <!-- 差异数量列 -->
              <template v-if="column.dataIndex === 'differenceQuantity'">
                <span
                  :style="{
                    color: getDifferenceColor(record.differenceQuantity),
                    fontWeight: 'bold',
                  }"
                >
                  {{ formatQuantity(record.differenceQuantity) }}
                </span>
              </template>

              <!-- 调整后库存列 -->
              <template v-if="column.dataIndex === 'afterQuantity'">
                <span style="color: #1890ff; font-weight: bold">
                  {{ formatQuantity(record.actualQuantity) }}
                </span>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 操作说明 -->
        <a-card title="调整说明" size="small" style="margin-top: 16px">
          <ul style="margin: 0; padding-left: 20px">
            <li>系统将根据实盘数量更新库存记录</li>
            <li>同时更新物料的总库存数量</li>
            <li>所有差异项目的状态将变更为"已调整"</li>
            <li>调整完成后，盘点单状态将变更为"已完成"</li>
            <li>此操作将记录操作人和操作时间</li>
          </ul>
        </a-card>

        <!-- 确认选项 -->
        <a-card size="small" style="margin-top: 16px">
          <a-checkbox v-model:checked="confirmAdjust">
            我已仔细核对上述差异明细，确认执行库存调整操作
          </a-checkbox>
        </a-card>
      </div>
    </a-modal>

    <!-- 初始库存录入弹窗 -->
    <a-modal
      v-model:open="initialStockVisible"
      title="初始库存录入"
      width="1200px"
      :footer="null"
      @cancel="handleInitialStockCancel"
    >
      <div>
        <!-- 操作说明 -->
        <a-alert
          message="初始库存录入说明"
          description="用于新建仓库或首次盘点时录入初始库存数据。录入的数据将直接创建库存记录。"
          type="info"
          show-icon
          style="margin-bottom: 16px"
        />

        <!-- 筛选条件 -->
        <a-card size="small" style="margin-bottom: 16px">
          <a-form layout="inline">
            <a-form-item label="仓库">
              <a-select
                v-model:value="initialForm.warehouse_id"
                placeholder="请选择仓库"
                style="width: 200px"
                @change="handleWarehouseChange"
              >
                <a-select-option
                  v-for="warehouse in warehouseOptions"
                  :key="warehouse.value"
                  :value="warehouse.value"
                >
                  {{ warehouse.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="物料">
              <a-input
                v-model:value="selectedMaterielText"
                placeholder="请选择物料"
                readonly
                style="width: 200px; cursor: pointer"
                @click="showMaterielSelector"
              />
              <manage-materiel-modelselector
                v-model:visible="materielSelectorVisible"
                :multiple="false"
                @selected="handleMaterielSelected"
              />
            </a-form-item>
            <a-form-item>
              <a-button
                type="primary"
                @click="handleAddStockItem"
                :disabled="!canAddItem"
              >
                添加库存项
              </a-button>
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 库存录入表格 -->
        <a-card title="库存录入明细" size="small">
          <template #extra>
            <a-space>
              <a-button
                @click="handleClearAll"
                :disabled="stockItems.length === 0"
              >
                清空全部
              </a-button>
              <a-button
                type="primary"
                @click="handleSubmitInitialStock"
                :loading="initialLoading"
                :disabled="stockItems.length === 0"
              >
                提交录入
              </a-button>
            </a-space>
          </template>

          <a-table
            :columns="initialStockColumns"
            :data-source="stockItems"
            :pagination="false"
            row-key="key"
            bordered
            size="small"
            :scroll="{ y: 400 }"
          >
            <template #bodyCell="{ column, record, index }">
              <!-- 序号列 -->
              <template v-if="column.dataIndex === 'index'">
                {{ index + 1 }}
              </template>

              <!-- 批号列 -->
              <template v-if="column.dataIndex === 'batch_no'">
                <a-input
                  v-model:value="record.batch_no"
                  placeholder="请输入批号"
                  size="small"
                />
              </template>

              <!-- 数量列 -->
              <template v-if="column.dataIndex === 'quantity'">
                <a-input-number
                  v-model:value="record.quantity"
                  :min="0"
                  :precision="4"
                  size="small"
                  style="width: 100%"
                  placeholder="请输入数量"
                />
              </template>

              <!-- 生产日期列 -->
              <template v-if="column.dataIndex === 'production_date'">
                <a-date-picker
                  v-model:value="record.production_date"
                  size="small"
                  style="width: 100%"
                  placeholder="选择生产日期"
                />
              </template>

              <!-- 有效期列 -->
              <template v-if="column.dataIndex === 'expiry_date'">
                <a-date-picker
                  v-model:value="record.expiry_date"
                  size="small"
                  style="width: 100%"
                  placeholder="选择有效期"
                />
              </template>

              <!-- 库位列 -->
              <template v-if="column.dataIndex === 'location'">
                <a-input
                  v-model:value="record.location"
                  placeholder="请输入库位"
                  size="small"
                />
              </template>

              <!-- 备注列 -->
              <template v-if="column.dataIndex === 'note'">
                <a-input
                  v-model:value="record.note"
                  placeholder="请输入备注"
                  size="small"
                />
              </template>

              <!-- 操作列 -->
              <template v-if="column.dataIndex === 'action'">
                <a-button
                  type="link"
                  size="small"
                  danger
                  @click="handleRemoveStockItem(index)"
                >
                  删除
                </a-button>
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from "vue";
  import { PlusOutlined, ReloadOutlined } from "@ant-design/icons-vue";
  import type {
    PaginationProps,
    TableProps,
    FormInstance,
  } from "ant-design-vue";
  import { message } from "ant-design-vue";
  import dayjs, { type Dayjs } from "dayjs";

  // 页面状态
  const loading = ref(false);
  const createLoading = ref(false);
  const dataSource = ref<any[]>([]);
  const current = ref(1);
  const total = ref(0);
  const pageSize = 10;

  // 弹窗状态
  const createModalVisible = ref(false);
  const createFormRef = ref<FormInstance>();
  const detailModalVisible = ref(false);
  const checkModalVisible = ref(false);
  const batchModalVisible = ref(false);
  const adjustConfirmVisible = ref(false);
  const initialStockVisible = ref(false);

  // 详情和盘点相关状态
  const currentStockCheck = ref<any>(null);
  const adjustStockCheck = ref<any>(null);
  const detailLoading = ref(false);
  const checkLoading = ref(false);
  const adjustLoading = ref(false);
  const saveLoading = ref(false);
  const batchLoading = ref(false);
  const initialLoading = ref(false);

  // 确认状态
  const confirmAdjust = ref(false);

  // 初始库存录入相关状态
  const initialForm = reactive({
    warehouse_id: undefined as number | undefined,
    materiel_id: undefined as number | undefined,
  });
  const stockItems = ref<any[]>([]);
  const materielSelectorVisible = ref(false);
  const selectedMaterielText = ref("");
  const selectedMaterielData = ref<any>(null);
  let stockItemKey = 0;

  // 搜索和筛选
  const detailSearchValue = ref("");
  const checkSearchValue = ref("");
  const checkStatusFilter = ref<string | undefined>(undefined);
  const showDifferenceOnly = ref(false);

  // 表格选择
  const selectedRowKeys = ref<number[]>([]);
  const changedItems = ref<Set<number>>(new Set());

  // 批量操作
  const batchNote = ref("");

  // 搜索表单
  const searchForm = reactive({
    checkNo: undefined as string | undefined,
    status: undefined as string | undefined,
    warehouseId: undefined as number | undefined,
  });

  // 创建表单
  const createForm = reactive({
    title: "",
    warehouse_id: undefined as number | undefined,
    checkType: "full" as "full" | "partial",
    plannedDate: undefined as Dayjs | undefined,
    note: "",
  });

  // 创建表单验证规则
  const createRules = {
    title: [{ required: true, message: "请输入盘点标题", trigger: "blur" }],
    warehouse_id: [
      { required: true, message: "请选择仓库", trigger: "change" },
    ],
    plannedDate: [
      { required: true, message: "请选择计划盘点日期", trigger: "change" },
    ],
  };

  // 仓库选项
  const warehouseOptions = ref<{ label: string; value: number }[]>([]);

  // 系统配置
  const systemConfig = ref<any>(null);

  // 分页配置
  const pagination = computed<PaginationProps>(() => ({
    current: current.value,
    total: total.value,
    pageSize: pageSize,
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
    showSizeChanger: false,
  }));

  // 计算属性
  const pendingCheckItems = computed(() => {
    return (
      currentStockCheck.value?.items?.filter(
        (item: any) => item.status === "pending"
      ) || []
    );
  });

  const filteredDetailItems = computed(() => {
    let result = currentStockCheck.value?.items || [];

    // 按搜索关键词筛选
    if (detailSearchValue.value) {
      const keyword = detailSearchValue.value.toLowerCase();
      result = result.filter(
        (item: any) =>
          item.materiel?.name?.toLowerCase().includes(keyword) ||
          item.materiel?.code?.toLowerCase().includes(keyword) ||
          item.batch_no?.toLowerCase().includes(keyword)
      );
    }

    return result;
  });

  const filteredCheckItems = computed(() => {
    let result = currentStockCheck.value?.items || [];

    // 按状态筛选
    if (checkStatusFilter.value) {
      result = result.filter(
        (item: any) => item.status === checkStatusFilter.value
      );
    }

    // 按搜索关键词筛选
    if (checkSearchValue.value) {
      const keyword = checkSearchValue.value.toLowerCase();
      result = result.filter(
        (item: any) =>
          item.materiel?.name?.toLowerCase().includes(keyword) ||
          item.materiel?.code?.toLowerCase().includes(keyword) ||
          item.batch_no?.toLowerCase().includes(keyword)
      );
    }

    // 只显示有差异的
    if (showDifferenceOnly.value) {
      result = result.filter(
        (item: any) =>
          item.differenceQuantity !== null &&
          Number(item.differenceQuantity) !== 0
      );
    }

    return result;
  });

  const checkRowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (keys: number[]) => {
      selectedRowKeys.value = keys;
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.status === "checked",
    }),
  };

  // 差异项目计算属性
  const differenceItems = computed(() => {
    return (
      adjustStockCheck.value?.items?.filter(
        (item: any) =>
          item.differenceQuantity !== null &&
          Number(item.differenceQuantity) !== 0
      ) || []
    );
  });

  // 初始库存录入计算属性
  const canAddItem = computed(() => {
    return initialForm.warehouse_id && selectedMaterielData.value;
  });

  // 权限检查计算属性
  const canCreateCheck = computed(() => {
    return systemConfig.value?.checkAble !== false;
  });

  const canInitialStock = computed(() => {
    return systemConfig.value?.checkInitAble !== false;
  });

  // 表格列定义
  const columns = [
    {
      title: "盘点单号",
      dataIndex: "checkNo",
      width: 150,
    },
    {
      title: "盘点标题",
      dataIndex: "title",
      width: 200,
    },
    {
      title: "仓库",
      dataIndex: ["warehouse", "name"],
      width: 120,
    },
    {
      title: "盘点类型",
      dataIndex: "checkType",
      width: 100,
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 100,
    },
    {
      title: "盘点进度",
      dataIndex: "progress",
      width: 150,
    },
    {
      title: "计划日期",
      dataIndex: "plannedDate",
      width: 120,
      customRender: ({ text }: any) => {
        return text ? dayjs(text).format("YYYY-MM-DD") : "-";
      },
    },
    {
      title: "盘点人",
      dataIndex: ["checkUser", "name"],
      width: 100,
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      width: 150,
      customRender: ({ text }: any) => {
        return dayjs(text).format("YYYY-MM-DD HH:mm");
      },
    },
    {
      title: "操作",
      dataIndex: "action",
      width: 200,
      fixed: "right" as const,
    },
  ];

  // 详情表格列定义
  const detailColumns = [
    {
      title: "物料编码",
      dataIndex: ["materiel", "code"],
      width: 120,
    },
    {
      title: "物料名称",
      dataIndex: ["materiel", "name"],
      width: 200,
    },
    {
      title: "批号",
      dataIndex: "batch_no",
      width: 120,
    },
    {
      title: "库位",
      dataIndex: "location",
      width: 100,
    },
    {
      title: "系统数量",
      dataIndex: "systemQuantity",
      width: 120,
      customRender: ({ text }: any) => formatQuantity(text),
    },
    {
      title: "实盘数量",
      dataIndex: "actualQuantity",
      width: 120,
      customRender: ({ text }: any) =>
        text !== null ? formatQuantity(text) : "-",
    },
    {
      title: "差异数量",
      dataIndex: "differenceQuantity",
      width: 120,
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 100,
    },
  ];

  // 盘点表格列定义
  const checkColumns = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
    },
    {
      title: "物料编码",
      dataIndex: ["materiel", "code"],
      width: 120,
    },
    {
      title: "物料名称",
      dataIndex: ["materiel", "name"],
      width: 200,
    },
    {
      title: "批号",
      dataIndex: "batch_no",
      width: 120,
    },
    {
      title: "库位",
      dataIndex: "location",
      width: 100,
    },
    {
      title: "系统数量",
      dataIndex: "systemQuantity",
      width: 120,
      customRender: ({ text }: any) => formatQuantity(text),
    },
    {
      title: "实盘数量",
      dataIndex: "actualQuantity",
      width: 120,
    },
    {
      title: "差异数量",
      dataIndex: "differenceQuantity",
      width: 120,
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 100,
    },
    {
      title: "操作",
      dataIndex: "action",
      width: 150,
    },
  ];

  // 初始库存录入表格列定义
  const initialStockColumns = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
    },
    {
      title: "仓库",
      dataIndex: ["warehouse", "name"],
      width: 120,
    },
    {
      title: "物料编码",
      dataIndex: ["materiel", "code"],
      width: 120,
    },
    {
      title: "物料名称",
      dataIndex: ["materiel", "name"],
      width: 200,
    },
    {
      title: "批号",
      dataIndex: "batch_no",
      width: 120,
    },
    {
      title: "数量",
      dataIndex: "quantity",
      width: 120,
    },
    {
      title: "生产日期",
      dataIndex: "production_date",
      width: 120,
    },
    {
      title: "有效期",
      dataIndex: "expiry_date",
      width: 120,
    },
    {
      title: "库位",
      dataIndex: "location",
      width: 100,
    },
    {
      title: "备注",
      dataIndex: "note",
      width: 150,
    },
    {
      title: "操作",
      dataIndex: "action",
      width: 80,
    },
  ];

  // API接口
  const queryStockCheckList =
    useApiTrpc().admin.stockcheck.queryStockCheckList.query;
  const createStockCheck =
    useApiTrpc().admin.stockcheck.createStockCheck.mutate;
  const completeStockCheck =
    useApiTrpc().admin.stockcheck.completeStockCheck.mutate;
  const getStockCheckDetail =
    useApiTrpc().admin.stockcheck.getStockCheckDetail.query;
  const updateStockCheckItem =
    useApiTrpc().admin.stockcheck.updateStockCheckItem.mutate;
  const getSystemConfig = useApiTrpc().admin.system.getSystemConfig.query;

  // 调整确认弹窗表格列定义
  const adjustColumns = [
    {
      title: "物料编码",
      dataIndex: ["materiel", "code"],
      width: 120,
    },
    {
      title: "物料名称",
      dataIndex: ["materiel", "name"],
      width: 200,
    },
    {
      title: "批号",
      dataIndex: "batch_no",
      width: 120,
    },
    {
      title: "系统数量",
      dataIndex: "systemQuantity",
      width: 120,
      customRender: ({ text }: any) => formatQuantity(text),
    },
    {
      title: "实盘数量",
      dataIndex: "actualQuantity",
      width: 120,
      customRender: ({ text }: any) => formatQuantity(text),
    },
    {
      title: "差异数量",
      dataIndex: "differenceQuantity",
      width: 120,
    },
    {
      title: "调整后库存",
      dataIndex: "afterQuantity",
      width: 120,
    },
  ];

  // 获取系统配置
  const fetchSystemConfig = async () => {
    try {
      const response = await getSystemConfig();
      if (response.code === 1 && response.data) {
        systemConfig.value = response.data;
      }
    } catch (error) {
      console.error("获取系统配置失败", error);
    }
  };

  // 获取仓库列表
  const fetchWarehouseList = async () => {
    try {
      const queryWarehouseList =
        useApiTrpc().admin.warehouse.queryWarehouseList.query;
      const response = await queryWarehouseList({
        take: 100,
        skip: 0,
      });

      if (response.data && response.data.result) {
        warehouseOptions.value = response.data.result.map((item: any) => ({
          value: item.id,
          label: item.name,
        }));
      }
    } catch (error) {
      console.error("获取仓库列表失败", error);
    }
  };

  // 获取数据
  const fetchData = async () => {
    loading.value = true;
    try {
      const params = {
        ...searchForm,
        take: pageSize,
        skip: (current.value - 1) * pageSize,
      };

      const response = await queryStockCheckList(params);

      if (response.code === 1 && response.data) {
        dataSource.value = response.data.result || [];
        total.value = response.data.total || 0;
        console.log("盘点单数据:", dataSource.value);
      } else {
        console.log("获取盘点单数据失败:", response);
      }
    } catch (error) {
      console.error("获取盘点单列表失败", error);
      message.error("获取盘点单列表失败");
    } finally {
      loading.value = false;
    }
  };

  // 表格变化处理
  const handleTableChange: TableProps["onChange"] = (pag: PaginationProps) => {
    current.value = pag.current || 1;
    fetchData();
  };

  // 搜索处理
  const handleSearch = () => {
    current.value = 1;
    fetchData();
  };

  // 重置处理
  const handleReset = () => {
    searchForm.checkNo = undefined;
    searchForm.status = undefined;
    searchForm.warehouseId = undefined;
    handleSearch();
  };

  // 状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      draft: "default",
      checking: "processing",
      completed: "success",
      cancelled: "error",
    };
    return colorMap[status] || "default";
  };

  // 状态文本
  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      draft: "草稿",
      checking: "盘点中",
      completed: "已完成",
      cancelled: "已取消",
    };
    return textMap[status] || status;
  };

  // 新建盘点
  const handleCreateCheck = () => {
    if (!canCreateCheck.value) {
      message.error("系统未开启库存盘点功能");
      return;
    }
    console.log("点击新建盘点");
    createModalVisible.value = true;
  };

  // 创建盘点单提交
  const handleCreateSubmit = async () => {
    try {
      await createFormRef.value?.validate();
      createLoading.value = true;

      if (!createForm.warehouse_id) {
        message.error("请选择仓库");
        return;
      }

      const params = {
        title: createForm.title,
        warehouse_id: createForm.warehouse_id,
        checkType: createForm.checkType,
        plannedDate: createForm.plannedDate?.format("YYYY-MM-DD") || "",
        note: createForm.note,
      };

      const response = await createStockCheck(params);

      if (response.code === 1) {
        message.success("盘点单创建成功");
        createModalVisible.value = false;
        handleCreateCancel();
        fetchData();
      }
    } catch (error) {
      console.error("创建盘点单失败", error);
      message.error("创建盘点单失败");
    } finally {
      createLoading.value = false;
    }
  };

  // 取消创建
  const handleCreateCancel = () => {
    createForm.title = "";
    createForm.warehouse_id = undefined;
    createForm.checkType = "full";
    createForm.plannedDate = undefined;
    createForm.note = "";
    createFormRef.value?.resetFields();
  };

  // 查看详情
  const handleViewDetail = async (record: any) => {
    console.log("查看详情点击", record);
    if (!record || !record.id) {
      message.error("盘点单数据异常");
      return;
    }

    try {
      detailLoading.value = true;
      const response = await getStockCheckDetail({ id: record.id });

      if (response.code === 1 && response.data) {
        currentStockCheck.value = response.data;
        detailModalVisible.value = true;
      }
    } catch (error) {
      console.error("获取盘点详情失败", error);
      message.error("获取盘点详情失败");
    } finally {
      detailLoading.value = false;
    }
  };

  // 开始盘点
  const handleStartCheck = async (record: any) => {
    console.log("开始盘点点击", record);
    if (!record || !record.id) {
      message.error("盘点单数据异常");
      return;
    }

    try {
      checkLoading.value = true;
      const response = await getStockCheckDetail({ id: record.id });

      if (response.code === 1 && response.data) {
        currentStockCheck.value = {
          ...response.data,
          items:
            response.data.items?.map((item: any) => ({
              ...item,
              actualQuantity: item.actualQuantity || item.systemQuantity, // 默认实盘数量等于系统数量
            })) || [],
        };
        checkModalVisible.value = true;
      }
    } catch (error) {
      console.error("获取盘点详情失败", error);
      message.error("获取盘点详情失败");
    } finally {
      checkLoading.value = false;
    }
  };

  // 完成盘点并调整库存
  const handleCompleteCheck = async (record: any) => {
    try {
      const response = await completeStockCheck({ id: record.id });
      if (response.code === 1) {
        message.success("库存调整完成");
        fetchData();
      }
    } catch (error) {
      console.error("库存调整失败", error);
      message.error("库存调整失败");
    }
  };

  // 格式化函数
  const formatDate = (date: string | null) => {
    return date ? dayjs(date).format("YYYY-MM-DD") : null;
  };

  const formatDateTime = (date: string | null) => {
    return date ? dayjs(date).format("YYYY-MM-DD HH:mm") : null;
  };

  const formatQuantity = (quantity: number | null) => {
    return quantity !== null ? Number(quantity).toFixed(4) : "-";
  };

  // 状态相关函数
  const getItemStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      pending: "default",
      checked: "success",
      adjusted: "blue",
    };
    return colorMap[status] || "default";
  };

  const getItemStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      pending: "待盘点",
      checked: "已盘点",
      adjusted: "已调整",
    };
    return textMap[status] || status;
  };

  const getDifferenceColor = (difference: number | null) => {
    if (difference === null || difference === 0) return "#666";
    return difference > 0 ? "#52c41a" : "#ff4d4f";
  };

  // 弹窗关闭处理
  const handleDetailCancel = () => {
    detailModalVisible.value = false;
    currentStockCheck.value = null;
    detailSearchValue.value = "";
  };

  const handleCheckCancel = () => {
    checkModalVisible.value = false;
    currentStockCheck.value = null;
    checkSearchValue.value = "";
    checkStatusFilter.value = undefined;
    showDifferenceOnly.value = false;
    selectedRowKeys.value = [];
    changedItems.value.clear();
  };

  // 打开盘点弹窗
  const handleOpenCheckModal = () => {
    checkModalVisible.value = true;
    detailModalVisible.value = false;
  };

  // 盘点相关函数
  const handleQuantityChange = (record: any) => {
    // 计算差异数量
    const systemQty = Number(record.systemQuantity);
    const actualQty = Number(record.actualQuantity || 0);
    record.differenceQuantity = actualQty - systemQty;

    // 标记为已更改
    changedItems.value.add(record.id);
  };

  const handleNextItem = async (currentRecord: any, currentIndex: number) => {
    // 自动确认当前项目并跳转到下一个待盘点项目
    await handleConfirmItem(currentRecord);

    const nextPendingItem = filteredCheckItems.value
      .slice(currentIndex + 1)
      .find((item: any) => item.status === "pending");

    if (nextPendingItem) {
      // 可以添加滚动到下一个项目的逻辑
      console.log("跳转到下一个项目:", nextPendingItem);
    }
  };

  const handleConfirmItem = async (record: any) => {
    if (record.actualQuantity === null || record.actualQuantity === undefined) {
      message.warning("请输入实盘数量");
      return;
    }

    try {
      const response = await updateStockCheckItem({
        id: record.id,
        actualQuantity: Number(record.actualQuantity),
        note: record.note || "",
      });

      if (response.code === 1) {
        record.status = "checked";
        record.checkedAt = new Date().toISOString();
        changedItems.value.delete(record.id);

        // 更新盘点单统计
        if (currentStockCheck.value) {
          currentStockCheck.value.checkedItems =
            currentStockCheck.value.items.filter(
              (item: any) => item.status === "checked"
            ).length;
          currentStockCheck.value.differenceItems =
            currentStockCheck.value.items.filter(
              (item: any) =>
                item.differenceQuantity !== null &&
                Number(item.differenceQuantity) !== 0
            ).length;
        }

        message.success("盘点确认成功");
      }
    } catch (error) {
      console.error("确认盘点失败", error);
      message.error("确认盘点失败");
    }
  };

  const handleEditItem = (record: any) => {
    record.status = "pending";
    record.checkedAt = null;
    changedItems.value.add(record.id);
    message.info("已重新开放编辑，请修改后重新确认");
  };

  const handleSaveAllChanges = async () => {
    if (changedItems.value.size === 0) {
      message.info("没有需要保存的更改");
      return;
    }

    saveLoading.value = true;
    try {
      const promises = Array.from(changedItems.value).map(async (itemId) => {
        const item = currentStockCheck.value?.items?.find(
          (i: any) => i.id === itemId
        );
        if (
          item &&
          item.actualQuantity !== null &&
          item.actualQuantity !== undefined
        ) {
          return updateStockCheckItem({
            id: item.id,
            actualQuantity: Number(item.actualQuantity),
            note: item.note || "",
          });
        }
      });

      await Promise.all(promises.filter(Boolean));

      message.success("所有更改已保存");
      changedItems.value.clear();

      // 重新获取数据
      const response = await getStockCheckDetail({
        id: currentStockCheck.value.id,
      });
      if (response.code === 1 && response.data) {
        currentStockCheck.value = response.data;
      }
    } catch (error) {
      console.error("保存失败", error);
      message.error("保存失败");
    } finally {
      saveLoading.value = false;
    }
  };

  const handleBatchCheck = () => {
    if (selectedRowKeys.value.length === 0) {
      message.warning("请选择要批量操作的项目");
      return;
    }
    batchModalVisible.value = true;
  };

  const handleBatchSubmit = async () => {
    batchLoading.value = true;
    try {
      const promises = selectedRowKeys.value.map(async (itemId) => {
        const item = currentStockCheck.value?.items?.find(
          (i: any) => i.id === itemId
        );
        if (item && item.status === "pending") {
          return updateStockCheckItem({
            id: item.id,
            actualQuantity: Number(item.systemQuantity), // 设置为系统数量（无差异）
            note: batchNote.value,
          });
        }
      });

      await Promise.all(promises.filter(Boolean));

      message.success(
        `批量操作完成，共处理 ${selectedRowKeys.value.length} 个项目`
      );
      batchModalVisible.value = false;
      selectedRowKeys.value = [];
      batchNote.value = "";

      // 重新获取数据
      const response = await getStockCheckDetail({
        id: currentStockCheck.value.id,
      });
      if (response.code === 1 && response.data) {
        currentStockCheck.value = response.data;
      }
    } catch (error) {
      console.error("批量操作失败", error);
      message.error("批量操作失败");
    } finally {
      batchLoading.value = false;
    }
  };

  const handleBatchCancel = () => {
    batchNote.value = "";
  };

  const handleMarkAllChecked = async () => {
    if (pendingCheckItems.value.length === 0) {
      message.info("没有待盘点的项目");
      return;
    }

    try {
      const promises = pendingCheckItems.value.map((item: any) =>
        updateStockCheckItem({
          id: item.id,
          actualQuantity: Number(item.systemQuantity), // 设置为系统数量（无差异）
          note: "批量标记为无差异",
        })
      );

      await Promise.all(promises);

      message.success(
        `已将 ${pendingCheckItems.value.length} 个项目标记为无差异`
      );

      // 重新获取数据
      const response = await getStockCheckDetail({
        id: currentStockCheck.value.id,
      });
      if (response.code === 1 && response.data) {
        currentStockCheck.value = response.data;
      }
    } catch (error) {
      console.error("批量标记失败", error);
      message.error("批量标记失败");
    }
  };

  // 显示库存调整确认弹窗
  const handleShowAdjustConfirm = async (record: any) => {
    console.log("显示库存调整确认弹窗", record);
    if (!record || !record.id) {
      message.error("盘点单数据异常");
      return;
    }

    if (record.status !== "completed") {
      message.error("盘点单未完成，无法调整库存");
      return;
    }

    if (!record.differenceItems || record.differenceItems === 0) {
      message.info("没有差异项目，无需调整库存");
      return;
    }

    try {
      // 获取最新的盘点单详情
      const response = await getStockCheckDetail({ id: record.id });

      if (response.code === 1 && response.data) {
        adjustStockCheck.value = response.data;
        confirmAdjust.value = false; // 重置确认状态
        adjustConfirmVisible.value = true;
      }
    } catch (error) {
      console.error("获取盘点详情失败", error);
      message.error("获取盘点详情失败");
    }
  };

  // 确认库存调整
  const handleConfirmAdjust = async () => {
    if (!confirmAdjust.value) {
      message.warning("请先确认已仔细核对差异明细");
      return;
    }

    if (!adjustStockCheck.value) {
      message.error("盘点单数据异常");
      return;
    }

    try {
      adjustLoading.value = true;
      const response = await completeStockCheck({
        id: adjustStockCheck.value.id,
      });

      if (response.code === 1) {
        message.success("库存调整完成");
        adjustConfirmVisible.value = false;

        // 刷新列表数据
        await fetchData();

        // 如果详情弹窗打开，也刷新详情数据
        if (detailModalVisible.value && currentStockCheck.value) {
          const detailResponse = await getStockCheckDetail({
            id: currentStockCheck.value.id,
          });
          if (detailResponse.code === 1 && detailResponse.data) {
            currentStockCheck.value = detailResponse.data;
          }
        }

        // 如果盘点弹窗打开，也刷新盘点数据
        if (checkModalVisible.value && currentStockCheck.value) {
          const checkResponse = await getStockCheckDetail({
            id: currentStockCheck.value.id,
          });
          if (checkResponse.code === 1 && checkResponse.data) {
            currentStockCheck.value = checkResponse.data;
          }
        }
      }
    } catch (error) {
      console.error("库存调整失败", error);
      message.error("库存调整失败");
    } finally {
      adjustLoading.value = false;
    }
  };

  // 取消库存调整
  const handleCancelAdjust = () => {
    adjustConfirmVisible.value = false;
    adjustStockCheck.value = null;
    confirmAdjust.value = false;
  };

  // 初始库存录入相关函数
  const handleInitialStock = () => {
    if (!canInitialStock.value) {
      message.error("系统未开启初始库存录入功能");
      return;
    }
    console.log("点击初始库存录入");
    initialStockVisible.value = true;
  };

  const handleInitialStockCancel = () => {
    initialStockVisible.value = false;
    initialForm.warehouse_id = undefined;
    initialForm.materiel_id = undefined;
    selectedMaterielText.value = "";
    selectedMaterielData.value = null;
    materielSelectorVisible.value = false;
    stockItems.value = [];
  };

  const handleWarehouseChange = () => {
    // 仓库变更时清空物料选择
    initialForm.materiel_id = undefined;
    selectedMaterielText.value = "";
    selectedMaterielData.value = null;
  };

  // 显示物料选择器
  const showMaterielSelector = () => {
    materielSelectorVisible.value = true;
  };

  // 处理物料选择
  const handleMaterielSelected = (selectedItems: any[]) => {
    if (selectedItems && selectedItems.length > 0) {
      const materiel = selectedItems[0];
      selectedMaterielData.value = materiel;
      selectedMaterielText.value = `${materiel.code} - ${materiel.name}`;
      initialForm.materiel_id = materiel.id;
    }
    materielSelectorVisible.value = false;
  };

  const handleAddStockItem = () => {
    if (!initialForm.warehouse_id || !selectedMaterielData.value) {
      message.warning("请先选择仓库和物料");
      return;
    }

    const warehouse = warehouseOptions.value.find(
      (w) => w.value === initialForm.warehouse_id
    );

    if (!warehouse) {
      message.error("仓库信息异常");
      return;
    }

    const newItem = {
      key: ++stockItemKey,
      warehouse: {
        id: warehouse.value,
        name: warehouse.label,
      },
      materiel: {
        id: selectedMaterielData.value.id,
        code: selectedMaterielData.value.code,
        name: selectedMaterielData.value.name,
      },
      batch_no: `BATCH${Date.now()}`, // 默认批号
      quantity: 0,
      production_date: null,
      expiry_date: null,
      location: "",
      note: "",
    };

    stockItems.value.push(newItem);
    message.success("库存项添加成功");
  };

  const handleRemoveStockItem = (index: number) => {
    stockItems.value.splice(index, 1);
    message.success("库存项删除成功");
  };

  const handleClearAll = () => {
    stockItems.value = [];
    message.success("已清空所有库存项");
  };

  const handleSubmitInitialStock = async () => {
    if (stockItems.value.length === 0) {
      message.warning("请至少添加一个库存项");
      return;
    }

    // 验证数据
    const invalidItems = stockItems.value.filter(
      (item) => !item.batch_no || !item.quantity || item.quantity <= 0
    );

    if (invalidItems.length > 0) {
      message.error("请确保所有库存项都有批号和有效数量");
      return;
    }

    try {
      initialLoading.value = true;

      // 调用批量创建库存的API
      const createInitialStock =
        useApiTrpc().admin.stock.createInitialStock.mutate;

      const stockData = stockItems.value.map((item) => ({
        materiel_id: item.materiel.id,
        warehouse_id: item.warehouse.id,
        batch_no: item.batch_no,
        quantity: Number(item.quantity),
        production_date: item.production_date
          ? item.production_date.format("YYYY-MM-DD")
          : null,
        expiry_date: item.expiry_date
          ? item.expiry_date.format("YYYY-MM-DD")
          : null,
        location: item.location || null,
        note: item.note || null,
      }));

      const response = await createInitialStock({ stockData });

      if (response.code === 1) {
        message.success(
          `初始库存录入成功，共录入 ${stockItems.value.length} 条记录`
        );
        handleInitialStockCancel();
      }
    } catch (error) {
      console.error("初始库存录入失败", error);
      message.error("初始库存录入失败");
    } finally {
      initialLoading.value = false;
    }
  };

  // 页面加载时获取数据
  onMounted(async () => {
    await fetchSystemConfig();
    await fetchWarehouseList();
    await fetchData();
  });
</script>

<style scoped>
  .search-form {
    background: #fafafa;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
  }
</style>
