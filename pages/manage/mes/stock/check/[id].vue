<template>
  <div class="stock-check-detail">
    <a-page-header
      :title="`盘点单详情 - ${stockCheck?.checkNo || ''}`"
      @back="handleBack"
    >
      <template #extra>
        <a-space>
          <a-button
            v-if="
              stockCheck?.status === 'draft' ||
              stockCheck?.status === 'checking'
            "
            type="primary"
            @click="handleStartCheck"
          >
            开始盘点
          </a-button>
          <a-button
            v-if="
              stockCheck?.status === 'completed' &&
              stockCheck?.differenceItems > 0
            "
            type="primary"
            @click="handleCompleteCheck"
            :loading="adjustLoading"
          >
            调整库存
          </a-button>
          <a-button @click="fetchData">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <a-card title="基本信息" style="margin-bottom: 16px">
      <a-descriptions :column="3" bordered>
        <a-descriptions-item label="盘点单号">
          {{ stockCheck?.checkNo }}
        </a-descriptions-item>
        <a-descriptions-item label="盘点标题">
          {{ stockCheck?.title }}
        </a-descriptions-item>
        <a-descriptions-item label="仓库">
          {{ stockCheck?.warehouse?.name }}
        </a-descriptions-item>
        <a-descriptions-item label="盘点类型">
          <a-tag :color="stockCheck?.checkType === 'full' ? 'blue' : 'orange'">
            {{ stockCheck?.checkType === "full" ? "全盘" : "抽盘" }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(stockCheck?.status)">
            {{ getStatusText(stockCheck?.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="盘点进度">
          <a-progress
            :percent="getProgress()"
            size="small"
            :status="stockCheck?.status === 'completed' ? 'success' : 'active'"
          />
          <span style="margin-left: 8px">
            {{ stockCheck?.checkedItems || 0 }}/{{
              stockCheck?.totalItems || 0
            }}
          </span>
        </a-descriptions-item>
        <a-descriptions-item label="计划日期">
          {{ formatDate(stockCheck?.plannedDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="实际日期">
          {{ formatDate(stockCheck?.actualDate) || "-" }}
        </a-descriptions-item>
        <a-descriptions-item label="盘点人">
          {{ stockCheck?.checkUser?.name }}
        </a-descriptions-item>
        <a-descriptions-item label="审核人">
          {{ stockCheck?.approveUser?.name || "-" }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ formatDateTime(stockCheck?.createdAt) }}
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="2">
          {{ stockCheck?.note || "-" }}
        </a-descriptions-item>
      </a-descriptions>
    </a-card>

    <a-card title="盘点明细">
      <template #extra>
        <a-space>
          <a-input-search
            v-model:value="searchValue"
            placeholder="搜索物料名称或编码"
            style="width: 250px"
            @search="handleSearch"
            allowClear
          />
          <a-select
            v-model:value="statusFilter"
            placeholder="筛选状态"
            style="width: 120px"
            allowClear
            @change="handleStatusFilter"
          >
            <a-select-option value="pending">待盘点</a-select-option>
            <a-select-option value="checked">已盘点</a-select-option>
            <a-select-option value="adjusted">已调整</a-select-option>
          </a-select>
        </a-space>
      </template>

      <a-table
        :columns="itemColumns"
        :data-source="filteredItems"
        :loading="loading"
        :pagination="false"
        row-key="id"
        bordered
        size="small"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <!-- 状态列 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getItemStatusColor(record.status)">
              {{ getItemStatusText(record.status) }}
            </a-tag>
          </template>

          <!-- 差异数量列 -->
          <template v-if="column.dataIndex === 'differenceQuantity'">
            <span
              :style="{
                color: getDifferenceColor(record.differenceQuantity),
                fontWeight: 'bold',
              }"
            >
              {{ formatQuantity(record.differenceQuantity) }}
            </span>
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-button
              v-if="record.status === 'pending' && canEdit"
              type="link"
              size="small"
              @click="handleEditItem(record)"
            >
              盘点
            </a-button>
            <span v-else-if="record.status === 'checked'">
              {{ formatDateTime(record.checkedAt) }}
            </span>
            <span v-else>-</span>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 盘点录入弹窗 -->
    <a-modal
      v-model:open="editModalVisible"
      title="录入盘点数量"
      width="500px"
      @ok="handleEditSubmit"
      @cancel="handleEditCancel"
      :confirm-loading="editLoading"
    >
      <a-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        layout="vertical"
      >
        <a-descriptions :column="1" bordered style="margin-bottom: 16px">
          <a-descriptions-item label="物料编码">
            {{ currentItem?.materiel?.code }}
          </a-descriptions-item>
          <a-descriptions-item label="物料名称">
            {{ currentItem?.materiel?.name }}
          </a-descriptions-item>
          <a-descriptions-item label="批号">
            {{ currentItem?.batch_no }}
          </a-descriptions-item>
          <a-descriptions-item label="库位">
            {{ currentItem?.location || "-" }}
          </a-descriptions-item>
          <a-descriptions-item label="系统数量">
            {{ formatQuantity(currentItem?.systemQuantity) }}
          </a-descriptions-item>
        </a-descriptions>

        <a-form-item name="actualQuantity" label="实盘数量">
          <a-input-number
            v-model:value="editForm.actualQuantity"
            :min="0"
            :precision="4"
            style="width: 100%"
            placeholder="请输入实盘数量"
          />
        </a-form-item>
        <a-form-item name="note" label="备注">
          <a-textarea
            v-model:value="editForm.note"
            placeholder="请输入备注"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from "vue";
  import { ReloadOutlined } from "@ant-design/icons-vue";
  import type { FormInstance } from "ant-design-vue";
  import { message } from "ant-design-vue";
  import dayjs from "dayjs";

  // 路由参数
  const route = useRoute();
  const stockCheckId = Number(route.params.id);

  // 页面状态
  const loading = ref(false);
  const adjustLoading = ref(false);
  const editLoading = ref(false);
  const stockCheck = ref<any>(null);
  const items = ref<any[]>([]);

  // 搜索和筛选
  const searchValue = ref("");
  const statusFilter = ref<string | undefined>(undefined);

  // 弹窗状态
  const editModalVisible = ref(false);
  const editFormRef = ref<FormInstance>();
  const currentItem = ref<any>(null);

  // 编辑表单
  const editForm = reactive({
    actualQuantity: undefined as number | undefined,
    note: "",
  });

  // 编辑表单验证规则
  const editRules = {
    actualQuantity: [
      { required: true, message: "请输入实盘数量", trigger: "blur" },
      {
        type: "number",
        min: 0,
        message: "实盘数量不能为负数",
        trigger: "blur",
      },
    ],
  };

  // 计算属性
  const canEdit = computed(() => {
    return (
      stockCheck.value?.status === "draft" ||
      stockCheck.value?.status === "checking"
    );
  });

  const filteredItems = computed(() => {
    let result = items.value;

    // 按状态筛选
    if (statusFilter.value) {
      result = result.filter((item) => item.status === statusFilter.value);
    }

    // 按搜索关键词筛选
    if (searchValue.value) {
      const keyword = searchValue.value.toLowerCase();
      result = result.filter(
        (item) =>
          item.materiel?.name?.toLowerCase().includes(keyword) ||
          item.materiel?.code?.toLowerCase().includes(keyword) ||
          item.batch_no?.toLowerCase().includes(keyword)
      );
    }

    return result;
  });

  // 表格列定义
  const itemColumns = [
    {
      title: "物料编码",
      dataIndex: ["materiel", "code"],
      width: 120,
      fixed: "left" as const,
    },
    {
      title: "物料名称",
      dataIndex: ["materiel", "name"],
      width: 200,
      fixed: "left" as const,
    },
    {
      title: "规格型号",
      dataIndex: ["materiel", "specification"],
      width: 120,
    },
    {
      title: "批号",
      dataIndex: "batch_no",
      width: 120,
    },
    {
      title: "库位",
      dataIndex: "location",
      width: 100,
    },
    {
      title: "系统数量",
      dataIndex: "systemQuantity",
      width: 120,
      customRender: ({ text }: any) => formatQuantity(text),
    },
    {
      title: "实盘数量",
      dataIndex: "actualQuantity",
      width: 120,
      customRender: ({ text }: any) =>
        text !== null ? formatQuantity(text) : "-",
    },
    {
      title: "差异数量",
      dataIndex: "differenceQuantity",
      width: 120,
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 100,
    },
    {
      title: "盘点人",
      dataIndex: ["checkUser", "name"],
      width: 100,
    },
    {
      title: "操作/盘点时间",
      dataIndex: "action",
      width: 150,
      fixed: "right" as const,
    },
  ];

  // API接口
  const getStockCheckDetail =
    useApiTrpc().admin.stockcheck.getStockCheckDetail.query;
  const updateStockCheckItem =
    useApiTrpc().admin.stockcheck.updateStockCheckItem.mutate;
  const completeStockCheck =
    useApiTrpc().admin.stockcheck.completeStockCheck.mutate;

  // 获取数据
  const fetchData = async () => {
    loading.value = true;
    try {
      const response = await getStockCheckDetail({ id: stockCheckId });

      if (response.code === 1 && response.data) {
        stockCheck.value = response.data;
        items.value = response.data.items || [];
      }
    } catch (error) {
      console.error("获取盘点详情失败", error);
      message.error("获取盘点详情失败");
    } finally {
      loading.value = false;
    }
  };

  // 格式化函数
  const formatDate = (date: string | null) => {
    return date ? dayjs(date).format("YYYY-MM-DD") : null;
  };

  const formatDateTime = (date: string | null) => {
    return date ? dayjs(date).format("YYYY-MM-DD HH:mm") : null;
  };

  const formatQuantity = (quantity: number | null) => {
    return quantity !== null ? Number(quantity).toFixed(4) : "-";
  };

  // 状态相关函数
  const getStatusColor = (status: string | undefined) => {
    const colorMap: Record<string, string> = {
      draft: "default",
      checking: "processing",
      completed: "success",
      cancelled: "error",
    };
    return colorMap[status || ""] || "default";
  };

  const getStatusText = (status: string | undefined) => {
    const textMap: Record<string, string> = {
      draft: "草稿",
      checking: "盘点中",
      completed: "已完成",
      cancelled: "已取消",
    };
    return textMap[status || ""] || status;
  };

  const getItemStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      pending: "default",
      checked: "success",
      adjusted: "blue",
    };
    return colorMap[status] || "default";
  };

  const getItemStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      pending: "待盘点",
      checked: "已盘点",
      adjusted: "已调整",
    };
    return textMap[status] || status;
  };

  const getDifferenceColor = (difference: number | null) => {
    if (difference === null || difference === 0) return "#666";
    return difference > 0 ? "#52c41a" : "#ff4d4f";
  };

  const getProgress = () => {
    if (!stockCheck.value) return 0;
    const { checkedItems = 0, totalItems = 1 } = stockCheck.value;
    return Math.round((checkedItems / totalItems) * 100);
  };

  // 事件处理函数
  const handleBack = () => {
    navigateTo("/manage/mes/stock/check");
  };

  const handleStartCheck = () => {
    navigateTo(`/manage/mes/stock/check/${stockCheckId}/check`);
  };

  const handleCompleteCheck = async () => {
    try {
      adjustLoading.value = true;
      const response = await completeStockCheck({ id: stockCheckId });

      if (response.code === 1) {
        message.success("库存调整完成");
        await fetchData();
      }
    } catch (error) {
      console.error("库存调整失败", error);
      message.error("库存调整失败");
    } finally {
      adjustLoading.value = false;
    }
  };

  const handleSearch = () => {
    // 搜索功能由computed属性自动处理
  };

  const handleStatusFilter = () => {
    // 筛选功能由computed属性自动处理
  };

  const handleEditItem = (record: any) => {
    currentItem.value = record;
    editForm.actualQuantity = record.actualQuantity || undefined;
    editForm.note = record.note || "";
    editModalVisible.value = true;
  };

  const handleEditSubmit = async () => {
    try {
      await editFormRef.value?.validate();
      editLoading.value = true;

      const response = await updateStockCheckItem({
        id: currentItem.value.id,
        actualQuantity: editForm.actualQuantity!,
        note: editForm.note,
      });

      if (response.code === 1) {
        message.success("盘点数量更新成功");
        editModalVisible.value = false;
        await fetchData();
      }
    } catch (error) {
      console.error("更新盘点数量失败", error);
      message.error("更新盘点数量失败");
    } finally {
      editLoading.value = false;
    }
  };

  const handleEditCancel = () => {
    editForm.actualQuantity = undefined;
    editForm.note = "";
    currentItem.value = null;
    editFormRef.value?.resetFields();
  };

  // 页面加载时获取数据
  onMounted(async () => {
    await fetchData();
  });
</script>

<style scoped>
  .stock-check-detail {
    padding: 0;
  }

  :deep(.ant-page-header) {
    padding: 16px 24px;
    background: #fff;
    margin-bottom: 16px;
  }

  :deep(.ant-descriptions-item-label) {
    font-weight: 500;
  }

  :deep(.ant-table-thead > tr > th) {
    background: #fafafa;
  }
</style>
