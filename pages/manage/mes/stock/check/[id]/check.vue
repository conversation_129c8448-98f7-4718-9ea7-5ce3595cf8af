<template>
  <div class="stock-check-execute">
    <a-page-header
      :title="`执行盘点 - ${stockCheck?.checkNo || ''}`"
      @back="handleBack"
    >
      <template #extra>
        <a-space>
          <a-button
            @click="handleSaveAll"
            :loading="saveLoading"
            type="primary"
          >
            保存所有更改
          </a-button>
          <a-button @click="fetchData">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <!-- 盘点进度卡片 -->
    <a-card style="margin-bottom: 16px">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-statistic
            title="总盘点项数"
            :value="stockCheck?.totalItems || 0"
            :value-style="{ color: '#1890ff' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="已盘点项数"
            :value="stockCheck?.checkedItems || 0"
            :value-style="{ color: '#52c41a' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="差异项数"
            :value="stockCheck?.differenceItems || 0"
            :value-style="{ color: '#ff4d4f' }"
          />
        </a-col>
        <a-col :span="6">
          <div style="text-align: center">
            <div style="margin-bottom: 8px; color: #666; font-size: 14px">
              盘点进度
            </div>
            <a-progress
              type="circle"
              :percent="getProgress()"
              :size="80"
              :status="
                stockCheck?.status === 'completed' ? 'success' : 'active'
              "
            />
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 快速操作栏 -->
    <a-card style="margin-bottom: 16px">
      <a-row :gutter="16" align="middle">
        <a-col :span="8">
          <a-input-search
            v-model:value="searchValue"
            placeholder="搜索物料名称、编码或批号"
            @search="handleSearch"
            allowClear
          />
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="statusFilter"
            placeholder="筛选状态"
            allowClear
            @change="handleStatusFilter"
          >
            <a-select-option value="pending">待盘点</a-select-option>
            <a-select-option value="checked">已盘点</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-checkbox v-model:checked="showDifferenceOnly">
            只显示有差异的
          </a-checkbox>
        </a-col>
        <a-col :span="8">
          <a-space>
            <a-button
              @click="handleBatchCheck"
              :disabled="selectedRowKeys.length === 0"
            >
              批量盘点
            </a-button>
            <a-button
              @click="handleMarkAllChecked"
              :disabled="pendingItems.length === 0"
            >
              全部标记为无差异
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 盘点明细表格 -->
    <a-card title="盘点明细">
      <a-table
        :columns="columns"
        :data-source="filteredItems"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        row-key="id"
        bordered
        size="small"
        :scroll="{ x: 1400 }"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record, index }">
          <!-- 序号列 -->
          <template v-if="column.dataIndex === 'index'">
            {{ (current - 1) * pageSize + index + 1 }}
          </template>

          <!-- 实盘数量列 -->
          <template v-if="column.dataIndex === 'actualQuantity'">
            <a-input-number
              v-if="record.status === 'pending'"
              v-model:value="record.actualQuantity"
              :min="0"
              :precision="4"
              size="small"
              style="width: 100%"
              @change="handleQuantityChange(record)"
              @pressEnter="handleNextItem(record)"
            />
            <span v-else>{{ formatQuantity(record.actualQuantity) }}</span>
          </template>

          <!-- 差异数量列 -->
          <template v-if="column.dataIndex === 'differenceQuantity'">
            <span
              :style="{
                color: getDifferenceColor(record.differenceQuantity),
                fontWeight: 'bold',
              }"
            >
              {{ formatQuantity(record.differenceQuantity) }}
            </span>
          </template>

          <!-- 状态列 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getItemStatusColor(record.status)">
              {{ getItemStatusText(record.status) }}
            </a-tag>
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button
                v-if="record.status === 'pending'"
                type="link"
                size="small"
                @click="handleConfirmItem(record)"
                :disabled="
                  record.actualQuantity === null ||
                  record.actualQuantity === undefined
                "
              >
                确认
              </a-button>
              <a-button
                v-if="record.status === 'checked'"
                type="link"
                size="small"
                @click="handleEditItem(record)"
              >
                修改
              </a-button>
              <a-popover title="备注" trigger="click" v-if="record.note">
                <template #content>
                  <div style="max-width: 200px">{{ record.note }}</div>
                </template>
                <a-button type="link" size="small">备注</a-button>
              </a-popover>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 批量盘点弹窗 -->
    <a-modal
      v-model:open="batchModalVisible"
      title="批量盘点"
      width="600px"
      @ok="handleBatchSubmit"
      @cancel="handleBatchCancel"
      :confirm-loading="batchLoading"
    >
      <a-alert
        message="批量操作提示"
        description="将为选中的所有项目设置相同的实盘数量等于系统数量（无差异）"
        type="info"
        style="margin-bottom: 16px"
      />
      <div>
        <p>
          已选择 <strong>{{ selectedRowKeys.length }}</strong> 个项目
        </p>
        <a-form layout="vertical">
          <a-form-item label="备注">
            <a-textarea
              v-model:value="batchNote"
              placeholder="请输入批量操作备注"
              :rows="3"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed, nextTick } from "vue";
  import { ReloadOutlined } from "@ant-design/icons-vue";
  import type { PaginationProps, TableProps } from "ant-design-vue";
  import { message } from "ant-design-vue";
  import dayjs from "dayjs";

  // 路由参数
  const route = useRoute();
  const stockCheckId = Number(route.params.id);

  // 页面状态
  const loading = ref(false);
  const saveLoading = ref(false);
  const batchLoading = ref(false);
  const stockCheck = ref<any>(null);
  const items = ref<any[]>([]);
  const changedItems = ref<Set<number>>(new Set());

  // 搜索和筛选
  const searchValue = ref("");
  const statusFilter = ref<string | undefined>(undefined);
  const showDifferenceOnly = ref(false);

  // 分页
  const current = ref(1);
  const total = ref(0);
  const pageSize = 20;

  // 表格选择
  const selectedRowKeys = ref<number[]>([]);

  // 批量操作
  const batchModalVisible = ref(false);
  const batchNote = ref("");

  // 计算属性
  const pendingItems = computed(() => {
    return items.value.filter((item) => item.status === "pending");
  });

  const filteredItems = computed(() => {
    let result = items.value;

    // 按状态筛选
    if (statusFilter.value) {
      result = result.filter((item) => item.status === statusFilter.value);
    }

    // 按搜索关键词筛选
    if (searchValue.value) {
      const keyword = searchValue.value.toLowerCase();
      result = result.filter(
        (item) =>
          item.materiel?.name?.toLowerCase().includes(keyword) ||
          item.materiel?.code?.toLowerCase().includes(keyword) ||
          item.batch_no?.toLowerCase().includes(keyword)
      );
    }

    // 只显示有差异的
    if (showDifferenceOnly.value) {
      result = result.filter(
        (item) =>
          item.differenceQuantity !== null &&
          Number(item.differenceQuantity) !== 0
      );
    }

    return result;
  });

  const pagination = computed<PaginationProps>(() => ({
    current: current.value,
    total: filteredItems.value.length,
    pageSize: pageSize,
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
    showSizeChanger: false,
  }));

  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (keys: number[]) => {
      selectedRowKeys.value = keys;
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.status === "checked",
    }),
  };

  // 表格列定义
  const columns = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      fixed: "left" as const,
    },
    {
      title: "物料编码",
      dataIndex: ["materiel", "code"],
      width: 120,
      fixed: "left" as const,
    },
    {
      title: "物料名称",
      dataIndex: ["materiel", "name"],
      width: 200,
      fixed: "left" as const,
    },
    {
      title: "规格型号",
      dataIndex: ["materiel", "specification"],
      width: 120,
    },
    {
      title: "批号",
      dataIndex: "batch_no",
      width: 120,
    },
    {
      title: "库位",
      dataIndex: "location",
      width: 100,
    },
    {
      title: "系统数量",
      dataIndex: "systemQuantity",
      width: 120,
      customRender: ({ text }: any) => formatQuantity(text),
    },
    {
      title: "实盘数量",
      dataIndex: "actualQuantity",
      width: 120,
    },
    {
      title: "差异数量",
      dataIndex: "differenceQuantity",
      width: 120,
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 100,
    },
    {
      title: "操作",
      dataIndex: "action",
      width: 150,
      fixed: "right" as const,
    },
  ];

  // API接口
  const getStockCheckDetail =
    useApiTrpc().admin.stockcheck.getStockCheckDetail.query;
  const updateStockCheckItem =
    useApiTrpc().admin.stockcheck.updateStockCheckItem.mutate;

  // 获取数据
  const fetchData = async () => {
    loading.value = true;
    try {
      const response = await getStockCheckDetail({ id: stockCheckId });

      if (response.code === 1 && response.data) {
        stockCheck.value = response.data;
        items.value =
          response.data.items?.map((item: any) => ({
            ...item,
            actualQuantity: item.actualQuantity || item.systemQuantity, // 默认实盘数量等于系统数量
          })) || [];
        total.value = items.value.length;
      }
    } catch (error) {
      console.error("获取盘点详情失败", error);
      message.error("获取盘点详情失败");
    } finally {
      loading.value = false;
    }
  };

  // 格式化函数
  const formatQuantity = (quantity: number | null) => {
    return quantity !== null ? Number(quantity).toFixed(4) : "-";
  };

  const getProgress = () => {
    if (!stockCheck.value) return 0;
    const { checkedItems = 0, totalItems = 1 } = stockCheck.value;
    return Math.round((checkedItems / totalItems) * 100);
  };

  // 状态相关函数
  const getItemStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      pending: "default",
      checked: "success",
      adjusted: "blue",
    };
    return colorMap[status] || "default";
  };

  const getItemStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      pending: "待盘点",
      checked: "已盘点",
      adjusted: "已调整",
    };
    return textMap[status] || status;
  };

  const getDifferenceColor = (difference: number | null) => {
    if (difference === null || difference === 0) return "#666";
    return difference > 0 ? "#52c41a" : "#ff4d4f";
  };

  // 事件处理函数
  const handleBack = () => {
    if (changedItems.value.size > 0) {
      message.warning("有未保存的更改，请先保存");
      return;
    }
    navigateTo(`/manage/mes/stock/check/${stockCheckId}`);
  };

  const handleQuantityChange = (record: any) => {
    // 计算差异数量
    const systemQty = Number(record.systemQuantity);
    const actualQty = Number(record.actualQuantity || 0);
    record.differenceQuantity = actualQty - systemQty;

    // 标记为已更改
    changedItems.value.add(record.id);
  };

  const handleNextItem = async (currentRecord: any) => {
    // 自动确认当前项目并跳转到下一个待盘点项目
    await handleConfirmItem(currentRecord);

    const currentIndex = items.value.findIndex(
      (item) => item.id === currentRecord.id
    );
    const nextPendingItem = items.value
      .slice(currentIndex + 1)
      .find((item) => item.status === "pending");

    if (nextPendingItem) {
      // 滚动到下一个项目
      await nextTick();
      const element = document.querySelector(
        `[data-row-key="${nextPendingItem.id}"]`
      );
      element?.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  };

  const handleConfirmItem = async (record: any) => {
    if (record.actualQuantity === null || record.actualQuantity === undefined) {
      message.warning("请输入实盘数量");
      return;
    }

    try {
      const response = await updateStockCheckItem({
        id: record.id,
        actualQuantity: Number(record.actualQuantity),
        note: record.note || "",
      });

      if (response.code === 1) {
        record.status = "checked";
        record.checkedAt = new Date().toISOString();
        changedItems.value.delete(record.id);

        // 更新盘点单统计
        if (stockCheck.value) {
          stockCheck.value.checkedItems = items.value.filter(
            (item) => item.status === "checked"
          ).length;
          stockCheck.value.differenceItems = items.value.filter(
            (item) =>
              item.differenceQuantity !== null &&
              Number(item.differenceQuantity) !== 0
          ).length;
        }

        message.success("盘点确认成功");
      }
    } catch (error) {
      console.error("确认盘点失败", error);
      message.error("确认盘点失败");
    }
  };

  const handleEditItem = (record: any) => {
    record.status = "pending";
    record.checkedAt = null;
    changedItems.value.add(record.id);
    message.info("已重新开放编辑，请修改后重新确认");
  };

  const handleSaveAll = async () => {
    if (changedItems.value.size === 0) {
      message.info("没有需要保存的更改");
      return;
    }

    saveLoading.value = true;
    try {
      const promises = Array.from(changedItems.value).map(async (itemId) => {
        const item = items.value.find((i) => i.id === itemId);
        if (
          item &&
          item.actualQuantity !== null &&
          item.actualQuantity !== undefined
        ) {
          return updateStockCheckItem({
            id: item.id,
            actualQuantity: Number(item.actualQuantity),
            note: item.note || "",
          });
        }
      });

      await Promise.all(promises.filter(Boolean));

      message.success("所有更改已保存");
      changedItems.value.clear();
      await fetchData();
    } catch (error) {
      console.error("保存失败", error);
      message.error("保存失败");
    } finally {
      saveLoading.value = false;
    }
  };

  const handleSearch = () => {
    // 搜索功能由computed属性自动处理
  };

  const handleStatusFilter = () => {
    // 筛选功能由computed属性自动处理
  };

  const handleTableChange = (pag: PaginationProps) => {
    current.value = pag.current || 1;
  };

  const handleBatchCheck = () => {
    if (selectedRowKeys.value.length === 0) {
      message.warning("请选择要批量操作的项目");
      return;
    }
    batchModalVisible.value = true;
  };

  const handleBatchSubmit = async () => {
    batchLoading.value = true;
    try {
      const promises = selectedRowKeys.value.map(async (itemId) => {
        const item = items.value.find((i) => i.id === itemId);
        if (item && item.status === "pending") {
          return updateStockCheckItem({
            id: item.id,
            actualQuantity: Number(item.systemQuantity), // 设置为系统数量（无差异）
            note: batchNote.value,
          });
        }
      });

      await Promise.all(promises.filter(Boolean));

      message.success(
        `批量操作完成，共处理 ${selectedRowKeys.value.length} 个项目`
      );
      batchModalVisible.value = false;
      selectedRowKeys.value = [];
      batchNote.value = "";
      await fetchData();
    } catch (error) {
      console.error("批量操作失败", error);
      message.error("批量操作失败");
    } finally {
      batchLoading.value = false;
    }
  };

  const handleBatchCancel = () => {
    batchNote.value = "";
  };

  const handleMarkAllChecked = async () => {
    if (pendingItems.value.length === 0) {
      message.info("没有待盘点的项目");
      return;
    }

    try {
      const promises = pendingItems.value.map((item) =>
        updateStockCheckItem({
          id: item.id,
          actualQuantity: Number(item.systemQuantity), // 设置为系统数量（无差异）
          note: "批量标记为无差异",
        })
      );

      await Promise.all(promises);

      message.success(`已将 ${pendingItems.value.length} 个项目标记为无差异`);
      await fetchData();
    } catch (error) {
      console.error("批量标记失败", error);
      message.error("批量标记失败");
    }
  };

  // 页面加载时获取数据
  onMounted(async () => {
    await fetchData();
  });
</script>

<style scoped>
  .stock-check-execute {
    padding: 0;
  }

  :deep(.ant-page-header) {
    padding: 16px 24px;
    background: #fff;
    margin-bottom: 16px;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background: #e6f7ff !important;
  }

  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-statistic-title) {
    font-size: 14px;
    margin-bottom: 4px;
  }

  :deep(.ant-statistic-content) {
    font-size: 20px;
    font-weight: 600;
  }
</style>
