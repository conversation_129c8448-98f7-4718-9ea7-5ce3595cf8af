<template>
  <div class="purchase-inbound-container">
    <a-card title="采购入库管理" :bordered="false">
      <!-- 表格 -->
      <manage-base-table
        ref="tableRef"
        :query="query"
        :columns="columns"
        :model="searchForm"
        rowKey="id"
      >
        <template #searchBox>
          <a-form-item name="orderNo" label="订单编号">
            <a-input
              v-model:value="searchForm.orderNo"
              placeholder="请输入订单编号"
            />
          </a-form-item>
          <a-form-item name="supplierName" label="供应商">
            <a-input
              v-model:value="searchForm.supplierName"
              placeholder="请输入供应商名称"
            />
          </a-form-item>
          <a-form-item name="status" label="订单状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              style="width: 150px"
              allowClear
            >
              <a-select-option
                v-for="item in orderStatus"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item name="dateRange" label="创建日期">
            <a-range-picker
              v-model:value="searchForm.dateRange"
              valueFormat="YYYY-MM-DD"
            />
          </a-form-item>
        </template>
        <!-- 状态列自定义渲染 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <!-- 操作列 -->
          <template v-if="column.key === 'action'">
            <a-space>
              <a
                @click="handleInbound(record)"
                v-if="
                  ['approved', 'partially_received'].includes(record.status)
                "
              >
                入库
              </a>
              <a
                @click="handleInboundHistory(record)"
                v-if="
                  ['partially_received', 'completed'].includes(record.status)
                "
              >
                入库记录
              </a>
              <a
                @click="handlePrintReceipt(record)"
                v-if="
                  ['partially_received', 'completed'].includes(record.status)
                "
              >
                打印入库单
              </a>
            </a-space>
          </template>
        </template>
      </manage-base-table>
    </a-card>

    <!-- 表单弹窗组件 -->
    <a-modal
      v-model:open="modalOpen"
      :title="modalTitle"
      width="80%"
      :footer="null"
      @cancel="handleCancel"
    >
      <!-- 表单组件或详情组件会在这里显示 -->
      <template v-if="modalType === 'view'">
        <ManagePurchaseOrderDetail :order-id="currentRecord.id" />
      </template>
      <template v-else-if="modalType === 'inbound'">
        <ManagePurchaseOrderReceiveForm
          :order-id="currentRecord.id"
          @success="handleInboundSuccess"
          @cancel="handleCancel"
          @print-receipt="handlePrintReceiptFromForm"
        />
      </template>
    </a-modal>

    <!-- 打印入库单模态框 -->
    <ManagePurchaseReceiptPrintModal
      v-model:open="printModalOpen"
      :receipt-id="selectedReceiptId"
    />
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, defineAsyncComponent } from "vue";
  import { message } from "ant-design-vue";
  import type { TableInstance } from "~/components/manage/base/table.client.vue";

  // 引入组件
  const ManagePurchaseOrderDetail = defineAsyncComponent(
    () => import("~/components/manage/purchase/OrderDetail.vue")
  );
  const ManagePurchaseOrderReceiveForm = defineAsyncComponent(
    () => import("~/components/manage/purchase/ReceiveForm.vue")
  );
  const ManagePurchaseReceiptPrintModal = defineAsyncComponent(
    () => import("~/components/manage/purchase/ReceiptPrintModal.vue")
  );

  // 定义订单状态选项
  const orderStatus = [
    { label: "已审核", value: "approved" },
    { label: "部分收货", value: "partially_received" },
    { label: "完成", value: "completed" },
  ];

  // 搜索表单数据
  const searchForm = reactive({
    orderNo: "",
    supplierName: "",
    status: undefined as string | undefined,
    dateRange: undefined as [string, string] | undefined,
  });

  // 定义订单记录类型
  interface PurchaseOrderRecord {
    id: number;
    orderNo: string;
    totalAmount: number;
    expectedDeliveryDate: string | null;
    status: string;
    supplier: {
      name: string;
    };
    warehouse?: {
      name: string;
    };
    [key: string]: any;
  }

  // 表格列定义
  const columns: ExtendedTableColumnProps<any>[] = [
    { title: "订单编号", dataIndex: "orderNo", key: "orderNo" },
    {
      title: "供应商",
      dataIndex: "supplierName",
      key: "supplierName",
      customRender: ({ record }: { record: PurchaseOrderRecord }) => {
        return record.supplier?.name || "无";
      },
    },
    { title: "订单金额", dataIndex: "totalAmount", key: "totalAmount" },
    {
      title: "仓库",
      dataIndex: "warehouseName",
      key: "warehouseName",
      customRender: ({ record }: { record: PurchaseOrderRecord }) => {
        return record.warehouse?.name || "无";
      },
    },
    {
      title: "预计到货日期",
      dataIndex: "expectedDeliveryDate",
      key: "expectedDeliveryDate",
    },
    { title: "状态", dataIndex: "status", key: "status" },
    { title: "操作", key: "action" },
  ];

  // 弹窗相关
  const modalOpen = ref(false);
  const modalTitle = ref("订单详情");
  const modalType = ref<string>("view");
  const currentRecord = ref<PurchaseOrderRecord>({} as PurchaseOrderRecord);

  // 打印相关
  const printModalOpen = ref(false);
  const selectedReceiptId = ref<number | undefined>(undefined);

  // 使用trpc查询接口
  const query = useApiTrpc().admin.stock.queryPurchaseOrders.query;
  const tableRef = ref<TableInstance>();

  // 根据状态值获取对应的文本
  const getStatusText = (status: string) => {
    const item = orderStatus.find((item) => item.value === status);
    return item ? item.label : "未知状态";
  };

  // 根据状态获取标签颜色
  const getStatusColor = (status: string) => {
    const statusColorMap: Record<string, string> = {
      approved: "success",
      partially_received: "warning",
      completed: "success",
    };
    return statusColorMap[status] || "default";
  };

  // 事件处理函数

  const handleInbound = (record: PurchaseOrderRecord) => {
    modalTitle.value = "采购入库";
    modalType.value = "inbound";
    currentRecord.value = { ...record };
    modalOpen.value = true;
  };

  const handleInboundHistory = (record: PurchaseOrderRecord) => {
    modalTitle.value = "入库记录";
    modalType.value = "view";
    currentRecord.value = { ...record };
    modalOpen.value = true;
  };

  const handleInboundSuccess = () => {
    modalOpen.value = false;
    tableRef.value?.query();
    message.success("入库操作成功");
  };

  const handleCancel = () => {
    modalOpen.value = false;
  };

  // 处理打印入库单（从表格操作）
  const handlePrintReceipt = async (record: PurchaseOrderRecord) => {
    try {
      // 获取该订单的入库记录
      const result = await useApiTrpc().admin.purchase.getReceiptRecords.query({
        orderId: record.id,
      });

      if (result.code === 200 && result.data.length > 0) {
        // 如果只有一条记录，直接打印
        if (result.data.length === 1) {
          selectedReceiptId.value = result.data[0].id;
          printModalOpen.value = true;
        } else {
          // 如果有多条记录，显示入库记录列表让用户选择
          handleInboundHistory(record);
          message.info("请在入库记录中选择要打印的记录");
        }
      } else {
        message.warning("该订单暂无入库记录");
      }
    } catch (error) {
      console.error("获取入库记录失败", error);
      message.error("获取入库记录失败，请稍后再试");
    }
  };

  // 处理打印入库单（从表单组件）
  const handlePrintReceiptFromForm = (receiptId: number) => {
    selectedReceiptId.value = receiptId;
    printModalOpen.value = true;
  };
</script>

<style scoped>
  /* .purchase-inbound-container {
    padding: 0 10px;
  } */

  .table-operations {
    margin-bottom: 16px;
    margin-top: 16px;
  }

  :deep(.ant-table-pagination.ant-pagination) {
    margin: 16px 0;
  }
</style>
