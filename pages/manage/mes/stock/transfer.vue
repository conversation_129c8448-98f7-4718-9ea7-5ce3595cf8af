<template>
  <a-card title="库存调拨">
    <!-- 数据表格 -->
    <manage-base-table
      ref="tableRef"
      :columns="columns"
      :query="queryStock"
      :model="searchForm"
      rowKey="id"
    >
      <template #searchBox>
        <a-form-item name="materialCode" label="物料编码">
          <a-input
            v-model:value="searchForm.materialCode"
            placeholder="请输入物料编码"
            allowClear
          />
        </a-form-item>
        <a-form-item name="materialName" label="物料名称">
          <a-input
            v-model:value="searchForm.materialName"
            placeholder="请输入物料名称"
            allowClear
          />
        </a-form-item>
        <a-form-item name="warehouseId" label="源仓库">
          <manage-warehouse-selector
            v-model:value="searchForm.warehouseId"
            placeholder="请选择仓库"
          />
        </a-form-item>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-button type="primary" @click="handleTransfer(record)"
            >调拨</a-button
          >
        </template>
      </template>
    </manage-base-table>

    <!-- 调拨模态框 -->
    <a-modal
      v-model:open="transferModalOpen"
      title="库存调拨"
      @ok="handleTransferSubmit"
      :confirmLoading="transferModalLoading"
      width="600px"
    >
      <a-form
        ref="transferFormRef"
        :model="transferForm"
        :rules="transferRules"
        layout="vertical"
      >
        <a-form-item label="物料信息">
          <a-descriptions bordered :column="1" size="small">
            <a-descriptions-item label="物料编码">
              {{ currentRecord?.materiel?.code || "-" }}
            </a-descriptions-item>
            <a-descriptions-item label="物料名称">
              {{ currentRecord?.materiel?.name || "-" }}
            </a-descriptions-item>
            <a-descriptions-item label="规格型号">
              {{ currentRecord?.materiel?.specification || "-" }}
            </a-descriptions-item>
            <a-descriptions-item label="当前库存">
              {{ currentRecord?.quantity || 0 }}
              {{ currentRecord?.materiel?.unit || "个" }}
            </a-descriptions-item>
            <a-descriptions-item label="批次号">
              {{ currentRecord?.batch_no || "-" }}
            </a-descriptions-item>
            <a-descriptions-item label="源仓库">
              {{ currentRecord?.warehouse?.name || "-" }}
            </a-descriptions-item>
          </a-descriptions>
        </a-form-item>

        <a-form-item label="目标仓库" name="targetWarehouseId">
          <manage-warehouse-selector
            v-model:value="transferForm.targetWarehouseId"
            :type="['production', 'finished', 'sales', 'waste']"
            placeholder="请选择目标仓库"
            :disabled="
              transferForm.sourceWarehouseId === transferForm.targetWarehouseId
            "
          />
        </a-form-item>

        <a-form-item label="调拨数量" name="quantity">
          <a-input-number
            v-model:value="transferForm.quantity"
            :min="1"
            :max="currentRecord?.quantity || 0"
            style="width: 100%"
            placeholder="请输入调拨数量"
          />
        </a-form-item>

        <a-form-item label="批次号" name="batchNo">
          <a-input
            v-model:value="transferForm.batchNo"
            placeholder="请输入批次号，默认使用原批次号"
          />
        </a-form-item>

        <a-form-item label="备注" name="note">
          <a-textarea
            v-model:value="transferForm.note"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from "vue";
  import { message } from "ant-design-vue";

  // 表格列定义
  const columns = [
    {
      title: "物料编码",
      dataIndex: ["materiel", "code"],
      width: 120,
    },
    {
      title: "物料名称",
      dataIndex: ["materiel", "name"],
      width: 150,
    },
    {
      title: "规格型号",
      dataIndex: ["materiel", "specification"],
      width: 120,
    },
    {
      title: "库存数量",
      dataIndex: "quantity",
      width: 100,
    },
    {
      title: "批号",
      dataIndex: "batch_no",
      width: 120,
    },
    {
      title: "单位",
      dataIndex: ["materiel", "unit"],
      width: 80,
    },
    {
      title: "仓库",
      dataIndex: ["warehouse", "name"],
      width: 120,
    },
    {
      title: "操作",
      key: "action",
      width: 100,
    },
  ];

  // 搜索表单
  const searchForm = reactive({
    materialCode: "",
    materialName: "",
    warehouseId: undefined as number | undefined,
  });

  // 调拨表单
  const transferForm = reactive({
    sourceWarehouseId: undefined as number | undefined,
    targetWarehouseId: undefined as number | undefined,
    materielId: undefined as number | undefined,
    batchNo: "",
    quantity: 1,
    note: "",
  });

  // 表单验证规则
  const transferRules = {
    targetWarehouseId: [{ required: true, message: "请选择目标仓库" }],
    quantity: [{ required: true, message: "请输入调拨数量" }],
    batchNo: [{ required: true, message: "请输入批次号" }],
  };

  // 模态框状态
  const transferModalOpen = ref(false);
  const transferModalLoading = ref(false);
  const transferFormRef = ref();
  const tableRef = ref();
  const currentRecord = ref<any>(null);

  // API接口
  const queryStock = useApiTrpc().admin.stock.queryStock.query;
  const stockTransfer = useApiTrpc().admin.stock.stockTransfer.mutate;

  // 打开调拨模态框
  const handleTransfer = (record: any) => {
    currentRecord.value = record;
    transferForm.sourceWarehouseId = record.warehouse_id;
    transferForm.materielId = record.materiel_id;
    transferForm.batchNo = record.batch_no;
    transferForm.quantity = 1;
    transferForm.targetWarehouseId = undefined;
    transferForm.note = "";

    transferModalOpen.value = true;
  };

  // 提交调拨
  const handleTransferSubmit = async () => {
    try {
      await transferFormRef.value.validate();

      transferModalLoading.value = true;

      // 检查源仓库和目标仓库不能相同
      if (transferForm.sourceWarehouseId === transferForm.targetWarehouseId) {
        message.error("源仓库和目标仓库不能相同");
        transferModalLoading.value = false;
        return;
      }

      // 检查调拨数量不能大于当前库存
      if (transferForm.quantity > currentRecord.value.quantity) {
        message.error("调拨数量不能大于当前库存");
        transferModalLoading.value = false;
        return;
      }

      // 执行库存调拨
      await stockTransfer({
        materiel_id: transferForm.materielId!,
        source_warehouse_id: transferForm.sourceWarehouseId!,
        target_warehouse_id: transferForm.targetWarehouseId!,
        source_batch_no: currentRecord.value.batch_no,
        target_batch_no: transferForm.batchNo || currentRecord.value.batch_no,
        quantity: transferForm.quantity,
        note: transferForm.note,
      });

      message.success("库存调拨成功");
      transferModalOpen.value = false;
      tableRef.value?.query();
    } catch (error: any) {
      console.error("库存调拨失败", error);
      message.error(error?.message || "库存调拨失败，请重试");
    } finally {
      transferModalLoading.value = false;
    }
  };

  // 页面初始化
  onMounted(() => {
    // 初始化页面数据
  });
</script>

<style scoped>
  .stock-transfer {
    padding: 24px;
  }
</style>
