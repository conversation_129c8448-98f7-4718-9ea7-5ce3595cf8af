<template>
  <a-card :title="pageTitle">
    <a-form
      :model="formState"
      :rules="rules"
      ref="formRef"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="标题" name="title">
        <a-input
          v-model:value="formState.title"
          placeholder="请输入任务标题"
          :disabled="isViewMode"
        />
      </a-form-item>

      <a-form-item label="任务类型" name="type">
        <a-select
          v-model:value="formState.type"
          placeholder="请选择任务类型"
          :disabled="isViewMode"
        >
          <a-select-option
            v-for="item in typeOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="预计时长" name="estimatedDuration">
        <a-input-number
          v-model:value="formState.estimatedDuration"
          placeholder="请输入预计时长"
          :min="0.1"
          :step="0.5"
          :precision="1"
          :disabled="isViewMode"
          style="width: 200px"
        />
        <span style="margin-left: 8px">小时</span>
      </a-form-item>

      <a-form-item label="优先级" name="priority">
        <a-select
          v-model:value="formState.priority"
          placeholder="请选择优先级"
          :disabled="isViewMode"
        >
          <a-select-option
            v-for="item in priorityOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="负责人" name="assignee_id">
        <manage-user-selector
          v-model="formState.assigneeUser"
          :multiple="false"
          :disabled="isViewMode"
        >
          <template #trigger="{ showModal }">
            <a-input
              :value="formState.assigneeUser?.name || ''"
              placeholder="请选择负责人"
              readonly
              @click="showModal"
              :disabled="isViewMode"
            >
              <template #suffix>
                <UserOutlined />
              </template>
            </a-input>
          </template>
        </manage-user-selector>
      </a-form-item>

      <a-form-item label="参与人员" name="participants">
        <manage-user-selector
          v-model="formState.participantUsers"
          :multiple="true"
          :disabled="isViewMode"
        >
          <template #trigger="{ showModal }">
            <a-input
              :value="selectedParticipantNames"
              placeholder="请选择参与人员"
              readonly
              @click="showModal"
              :disabled="isViewMode"
            >
              <template #suffix>
                <TeamOutlined />
              </template>
            </a-input>
          </template>
        </manage-user-selector>
        <div v-if="formState.participantUsers.length > 0" style="margin-top: 8px">
          <a-tag
            v-for="user in formState.participantUsers"
            :key="user.id"
            closable
            @close="removeParticipant(user.id)"
            :style="{ marginBottom: '4px' }"
          >
            {{ user.name }}
          </a-tag>
        </div>
      </a-form-item>

      <a-form-item label="说明" name="description">
        <a-textarea
          v-model:value="formState.description"
          placeholder="请输入任务说明"
          :rows="4"
          :disabled="isViewMode"
        />
      </a-form-item>

      <a-form-item :wrapper-col="{ offset: 4, span: 16 }" v-if="!isViewMode">
        <a-space>
          <a-button type="primary" @click="handleSubmit" :loading="loading">
            {{ formState.id ? '更新' : '创建' }}
          </a-button>
          <a-button @click="handleCancel">取消</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-card>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { UserOutlined, TeamOutlined } from '@ant-design/icons-vue';
import type { Rule } from 'ant-design-vue/es/form';
import type { User } from '~/types/user';


interface TaskFormState {
  id?: number;
  title: string;
  type?: string;
  estimatedDuration?: number;
  description: string;
  priority: string;
  assignee_id?: number;
  assigneeUser?: User;
  participants: number[];
  participantUsers: User[];
}

const router = useRouter();
const route = useRoute();
const formRef = ref();
const loading = ref(false);

const isViewMode = computed(() => route.query.mode === 'view');
const pageTitle = computed(() => {
  if (isViewMode.value) return '查看任务工单';
  return formState.id ? '编辑任务工单' : '创建任务工单';
});

// 表单状态
const formState = reactive<TaskFormState>({
  title: '',
  type: undefined,
  estimatedDuration: undefined,
  description: '',
  priority: 'normal',
  assignee_id: undefined,
  assigneeUser: undefined,
  participants: [],
  participantUsers: [],
});

// 任务类型选项
const typeOptions = [
  { value: '配料', label: '配料' },
  { value: '换模具', label: '换模具' },
  { value: '准备', label: '准备' },
  { value: '卸车', label: '卸车' },
  { value: '发货', label: '发货' },
  { value: '粉料', label: '粉料' },
  { value: '其他', label: '其他' },
];

// 优先级选项
const priorityOptions = [
  { value: 'low', label: '低' },
  { value: 'normal', label: '普通' },
  { value: 'high', label: '高' },
  { value: 'urgent', label: '紧急' },
];

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入任务标题' } as Rule,
  ],
  type: [
    { required: true, message: '请选择任务类型' } as Rule,
  ],
  estimatedDuration: [
    { required: true, message: '请输入预计时长' } as Rule,
    { type: 'number', min: 0.1, message: '预计时长至少为0.1小时' } as Rule,
  ],
  priority: [
    { required: true, message: '请选择优先级' } as Rule,
  ],
  participants: [
    { 
      validator: async (rule: Rule, value: any) => {
        if (!formState.participantUsers || formState.participantUsers.length === 0) {
          return Promise.reject('请至少选择一名参与人员');
        }
        return Promise.resolve();
      },
    } as any,
  ],
};

// 显示选中的参与人员姓名
const selectedParticipantNames = computed(() => {
  if (!formState.participantUsers?.length) return '';
  return formState.participantUsers.map((user) => user.name).join(', ');
});



watch(
  () => formState.assigneeUser,
  (newUser) => {
    formState.assignee_id = newUser?.id;
  }
);

watch(
  () => formState.participantUsers,
  (newUsers) => {
    formState.participants = newUsers.map((user) => user.id);
  },
  { deep: true }
);

// 移除参与人员
const removeParticipant = (userId: number) => {
  formState.participantUsers = formState.participantUsers.filter(user => user.id !== userId);
  formState.participants = formState.participants.filter(id => id !== userId);
};

// 初始化表单
onMounted(() => {
  const id = route.query.id;
  if (id) {
    loadTaskData(Number(id));
  }
});

// 加载任务工单数据
const loadTaskData = async (id: number) => {
  try {
    const result = await useApiTrpc().admin.taskworkorder.getTaskWorkOrder.query({ id });

    if (result && result.code === 1) {
      const task = result.data;

      // 设置表单数据
      Object.assign(formState, {
        id: task.id,
        title: task.title,
        type: task.type,
        estimatedDuration: task.estimatedDuration,
        description: task.description || '',
        priority: task.priority,
        assignee_id: task.assignee_id,
        assigneeUser: task.assignee as User | undefined,
        participants: task.participants?.map((p: any) => p.user_id) || [],
        participantUsers: task.participants?.map((p: any) => p.user) || [],
      });
    }
  } catch (error) {
    console.error('加载任务工单数据失败:', error);
    message.error('加载数据失败');
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    const submitData = {
      title: formState.title,
      type: formState.type,
      estimatedDuration: formState.estimatedDuration!,
      description: formState.description,
      priority: formState.priority,
      assignee_id: formState.assignee_id,
      participants: formState.participants,
    };

    if (formState.id) {
      // 更新
      await useApiTrpc().admin.taskworkorder.updateTaskWorkOrder.mutate({
        id: formState.id,
        ...submitData,
      });
      message.success('更新成功');
    } else {
      // 创建
      await useApiTrpc().admin.taskworkorder.createTaskWorkOrder.mutate(submitData);
      message.success('创建成功');
    }

    // 返回列表页
    router.push('/manage/mes/production/worktask');
  } catch (error) {
    console.error('保存任务工单失败:', error);
    message.error(formState.id ? '更新失败' : '创建失败');
  } finally {
    loading.value = false;
  }
};

// 取消编辑
const handleCancel = () => {
  router.push('/manage/mes/production/worktask');
};
</script>
