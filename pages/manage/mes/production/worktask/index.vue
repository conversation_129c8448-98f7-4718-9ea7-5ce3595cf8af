<template>
  <a-card title="任务工单管理">
    <template #extra>
      <a-button type="primary" @click="handleCreateTask">新建任务工单</a-button>
    </template>

    <!-- 表格区域 -->
    <manage-base-table
        :columns="columns"
        :query="query"
        :model="searchForm"
        rowKey="id"
        quickSearch
        quickSearchPlaceholder="输入标题查询"
    >
      <template #searchBox>
        <a-form-item name="title" label="标题">
          <manage-base-search-input
              v-model:value="searchForm.title"
              placeholder="请输入标题"
          />
        </a-form-item>
        <a-form-item name="type" label="任务类型">
          <a-select
              v-model:value="searchForm.type"
              placeholder="请选择任务类型"
              allow-clear
              style="width: 200px"
          >
            <a-select-option
                v-for="item in typeOptions"
                :key="item.value"
                :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="status" label="状态">
          <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              allow-clear
              style="width: 200px"
          >
            <a-select-option
                v-for="item in statusOptions"
                :key="item.value"
                :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="priority" label="优先级">
          <a-select
              v-model:value="searchForm.priority"
              placeholder="请选择优先级"
              allow-clear
              style="width: 200px"
          >
            <a-select-option
                v-for="item in priorityOptions"
                :key="item.value"
                :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'type'">
          <a-tag :color="getTypeColor(record.type)">
            {{ record.type }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'priority'">
          <a-tag :color="getPriorityColor(record.priority)">
            {{ getPriorityText(record.priority) }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'estimatedDuration'">
          {{ record.estimatedDuration }}小时
        </template>
        <template v-else-if="column.dataIndex === 'actualDuration'">
          {{ record.actualDuration ? record.actualDuration + '小时' : '-' }}
        </template>
        <template v-else-if="column.dataIndex === 'createUser'">
          {{ record.createUser?.name || '-' }}
        </template>
        <template v-else-if="column.dataIndex === 'assignee'">
          {{ record.assignee?.name || '-' }}
        </template>
        <template v-else-if="column.dataIndex === 'createdAt'">
          {{ dayjs(record.createdAt).format('YYYY-MM-DD HH:mm') }}
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleView(record)">
              查看
            </a-button>
            <a-button
                type="link"
                size="small"
                @click="handleEdit(record)"
                v-if="record.status === 'draft'"
            >
              编辑
            </a-button>
            <a-dropdown v-if="['draft', 'pending', 'in_progress'].includes(record.status)">
              <template #overlay>
                <a-menu @click="({ key }) => handleStatusChange(record, key)">
                  <a-menu-item
                      key="pending"
                      v-if="record.status === 'draft'"
                  >
                    开始执行
                  </a-menu-item>
                  <a-menu-item
                      key="in_progress"
                      v-if="record.status === 'pending'"
                  >
                    进行中
                  </a-menu-item>
                  <a-menu-item
                      key="completed"
                      v-if="record.status === 'in_progress'"
                  >
                    完成
                  </a-menu-item>
                  <a-menu-item
                      key="cancelled"
                      v-if="['draft', 'pending', 'in_progress'].includes(record.status)"
                  >
                    取消
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                状态操作 <DownOutlined />
              </a-button>
            </a-dropdown>
            <a-button
                type="link"
                size="small"
                danger
                @click="handleDelete(record)"
                v-if="record.status === 'draft'"
            >
              删除
            </a-button>
          </a-space>
        </template>
      </template>
    </manage-base-table>

    <!-- 任务工单详情模态框 -->
    <a-modal
        v-model:open="taskDetailModalVisible"
        title="任务工单详情"
        width="800px"
        :footer="null"
    >
      <div v-if="currentTask">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="标题">
            {{ currentTask.title }}
          </a-descriptions-item>
          <a-descriptions-item label="类型">
            <a-tag :color="getTypeColor(currentTask.type)">
              {{ currentTask.type }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(currentTask.status)">
              {{ getStatusText(currentTask.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(currentTask.priority)">
              {{ getPriorityText(currentTask.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="预计时长">
            {{ currentTask.estimatedDuration }}小时
          </a-descriptions-item>
          <a-descriptions-item label="实际时长">
            {{ currentTask.actualDuration ? currentTask.actualDuration + '小时' : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="创建人">
            {{ currentTask.createUser?.name || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="负责人">
            {{ currentTask.assignee?.name || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ dayjs(currentTask.createdAt).format('YYYY-MM-DD HH:mm:ss') }}
          </a-descriptions-item>
          <a-descriptions-item label="开始时间">
            {{ currentTask.actualStartAt ? dayjs(currentTask.actualStartAt).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="结束时间">
            {{ currentTask.actualEndAt ? dayjs(currentTask.actualEndAt).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="说明" :span="2">
            {{ currentTask.description || '-' }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>参与人员</a-divider>
        <a-list
            :data-source="currentTask.participants || []"
            size="small"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #title>
                  {{ item.user?.name }}
                </template>
                <template #description>
                  {{ item.role === 'participant' ? '参与者' : '负责人' }}
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { DownOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import type { TaskWorkOrderDetail } from '~/schemas/taskworkorder';

definePageMeta({

  middleware: "auth",
});

const router = useRouter();

// 搜索表单
const searchForm = reactive({
  title: '',
  type: '',
  status: '',
  priority: '',
});

// 当前选中的任务工单
const currentTask = ref<TaskWorkOrderDetail | null>(null);
const taskDetailModalVisible = ref(false);

// 任务类型选项
const typeOptions = [
  { value: '配料', label: '配料' },
  { value: '换模具', label: '换模具' },
  { value: '准备', label: '准备' },
  { value: '卸车', label: '卸车' },
  { value: '发货', label: '发货' },
  { value: '粉料', label: '粉料' },
  { value: '其他', label: '其他' },
];

// 状态选项
const statusOptions = [
  { value: 'draft', label: '草稿' },
  { value: 'pending', label: '待执行' },
  { value: 'in_progress', label: '执行中' },
  { value: 'completed', label: '已完成' },
  { value: 'cancelled', label: '已取消' },
];

// 优先级选项
const priorityOptions = [
  { value: 'low', label: '低' },
  { value: 'normal', label: '普通' },
  { value: 'high', label: '高' },
  { value: 'urgent', label: '紧急' },
];

// 表格列定义
const columns = [
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    width: 100,
  },
  {
    title: '预计时长',
    dataIndex: 'estimatedDuration',
    key: 'estimatedDuration',
    width: 100,
  },
  {
    title: '实际时长',
    dataIndex: 'actualDuration',
    key: 'actualDuration',
    width: 100,
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    key: 'createUser',
    width: 100,
  },
  {
    title: '负责人',
    dataIndex: 'assignee',
    key: 'assignee',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 200,
    fixed: 'right',
  },
];

// API查询函数
const query = useApiTrpc().admin.taskworkorder.queryTaskWorkOrder.query;

// 工具函数
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    '配料': 'blue',
    '换模具': 'green',
    '准备': 'orange',
    '卸车': 'purple',
    '发货': 'cyan',
    '粉料': 'magenta',
    '其他': 'default',
  };
  return colorMap[type] || 'default';
};

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'draft': 'default',
    'pending': 'orange',
    'in_progress': 'blue',
    'completed': 'green',
    'cancelled': 'red',
  };
  return colorMap[status] || 'default';
};

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'draft': '草稿',
    'pending': '待执行',
    'in_progress': '执行中',
    'completed': '已完成',
    'cancelled': '已取消',
  };
  return textMap[status] || status;
};

const getPriorityColor = (priority: string) => {
  const colorMap: Record<string, string> = {
    'low': 'default',
    'normal': 'blue',
    'high': 'orange',
    'urgent': 'red',
  };
  return colorMap[priority] || 'default';
};

const getPriorityText = (priority: string) => {
  const textMap: Record<string, string> = {
    'low': '低',
    'normal': '普通',
    'high': '高',
    'urgent': '紧急',
  };
  return textMap[priority] || priority;
};

// 事件处理函数
const handleCreateTask = () => {
  router.push('/manage/mes/production/worktask/create');
};

const handleView = async (record: any) => {
  try {
    const result = await useApiTrpc().admin.taskworkorder.getTaskWorkOrder.query({
      id: record.id,
    });

    if (result && result.code === 1) {
      currentTask.value = result.data;
      taskDetailModalVisible.value = true;
    }
  } catch (error) {
    console.error('获取任务工单详情失败:', error);
    message.error('获取详情失败');
  }
};

const handleEdit = (record: any) => {
  router.push(`/manage/mes/production/worktask/create?id=${record.id}`);
};

const handleDelete = async (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这个任务工单吗？删除后无法恢复。',
    onOk: async () => {
      try {
        await useApiTrpc().admin.taskworkorder.deleteTaskWorkOrder.mutate({
          id: record.id,
        });
        message.success('删除成功');
        // 刷新列表
        window.location.reload();
      } catch (error) {
        console.error('删除任务工单失败:', error);
        message.error('删除失败');
      }
    },
  });
};

const handleStatusChange = async (record: any, newStatus: string) => {
  const statusText = getStatusText(newStatus);

  Modal.confirm({
    title: '确认状态变更',
    content: `确定要将任务工单状态更改为"${statusText}"吗？`,
    onOk: async () => {
      try {
        const updateData: any = {
          id: record.id,
          status: newStatus,
        };

        // 根据状态设置时间
        if (newStatus === 'in_progress' && !record.actualStartAt) {
          updateData.actualStartAt = new Date().toISOString();
        } else if (newStatus === 'completed' && !record.actualEndAt) {
          updateData.actualEndAt = new Date().toISOString();

          // 计算实际时长
          if (record.actualStartAt) {
            const startTime = new Date(record.actualStartAt);
            const endTime = new Date();
            const duration = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60); // 转换为小时
            updateData.actualDuration = Math.round(duration * 100) / 100; // 保留两位小数
          }
        }

        await useApiTrpc().admin.taskworkorder.updateTaskWorkOrderStatus.mutate(updateData);
        message.success('状态更新成功');
        // 刷新列表
        window.location.reload();
      } catch (error) {
        console.error('更新状态失败:', error);
        message.error('状态更新失败');
      }
    },
  });
};
</script>