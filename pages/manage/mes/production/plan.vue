<template>
  <a-card title="生产计划">
    <template #extra>
      <manage-base-modalform
        :model="formState"
        @submit="handleOk"
        v-model:open="modalOpen"
        @show="handleShow"
      >
        <template #default="{ show }">
          <a-button @click="show"> <plus-outlined /> 添加 </a-button>
        </template>
        <template #form>
          <a-form-item label="编码" name="code">
            <a-input v-model:value="formState.code" disabled />
          </a-form-item>
          <a-form-item label="名称" name="name">
            <a-input v-model:value="formState.name" />
          </a-form-item>
          <a-form-item label="开始时间" name="startTime">
            <a-date-picker
              v-model:value="formState.startTime"
              format="YYYY-MM-DD"
              style="width: 100%"
              :valueType="'string'"
            />
          </a-form-item>
          <a-form-item label="结束时间" name="endTime">
            <a-date-picker
              v-model:value="formState.endTime"
              format="YYYY-MM-DD"
              style="width: 100%"
              :valueType="'string'"
            />
          </a-form-item>
          <a-form-item label="描述" name="description">
            <a-textarea v-model:value="formState.description" />
          </a-form-item>
          <a-form-item label="生产物料" name="items">
            <div>
              <a-button
                style="margin-bottom: 16px"
                @click="showMaterielSelector"
              >
                <plus-outlined /> 添加物料
              </a-button>
              <manage-materiel-shoppingcar
                v-model:materials="formState.items"
              />
            </div>
          </a-form-item>
        </template>
      </manage-base-modalform>
    </template>

    <manage-base-table ref="tableRef" :columns="columns" :query="query">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'completion'">
          <a-progress
            :stroke-color="{
              '0%': '#108ee9',
              '100%': '#87d068',
            }"
            :percent="record.completion"
          />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <template v-if="record.status === 0">
              <a-button type="link" @click="viewPlan.show(record)"
                >发布计划
              </a-button>
            </template>
            <template v-else>
              <a-button type="link" @click="viewPlan.show(record)"
                >查看</a-button
              >
            </template>
            <template v-if="record.status !== 2">
              <a-popconfirm
                title="确定要终止这个生产计划吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleTerminate(record)"
              >
                <a-button type="link">终止</a-button>
              </a-popconfirm>
            </template>
          </a-space>
        </template>
      </template>
    </manage-base-table>

    <manage-materiel-modelselector
      v-model:visible="materielSelectorOpen"
      :multiple="true"
      @selected="onMaterielSelect"
    />
    <a-drawer
      v-model:visible="viewPlan.visible"
      :title="`生产计划-${viewPlan.record.name}`"
      width="50%"
    >
      <template #extra>
        <a-space>
          <a-button
            type="primary"
            v-if="viewPlan.record.status === 0"
            @click="handlePublish"
            :loading="publishLoading"
          >
            发布计划
          </a-button>
        </a-space>
      </template>
      <a-descriptions bordered :column="2">
        <a-descriptions-item label="编码">
          {{ viewPlan.record.code }}
        </a-descriptions-item>
        <a-descriptions-item label="名称">
          {{ viewPlan.record.name }}
        </a-descriptions-item>
        <a-descriptions-item label="开始时间">
          {{ dayjs(viewPlan.record.startTime).format("YYYY-MM-DD") }}
        </a-descriptions-item>
        <a-descriptions-item label="结束时间">
          {{ dayjs(viewPlan.record.endTime).format("YYYY-MM-DD") }}
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(viewPlan.record.status)">
            {{ getStatusText(viewPlan.record.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ dayjs(viewPlan.record.createAt).format("YYYY-MM-DD HH:mm:ss") }}
        </a-descriptions-item>
        <a-descriptions-item label="描述">
          {{ viewPlan.record.description || "暂无描述" }}
        </a-descriptions-item>
      </a-descriptions>

      <div style="margin-top: 24px">
        <div style="margin-bottom: 16px">
          <h3>生产物料明细</h3>
        </div>
        <a-table
          :dataSource="viewPlan.record.ProductionPlanItem"
          :columns="viewMaterielColumns"
          :pagination="false"
        />
      </div>
    </a-drawer>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, reactive } from "vue";
  import type { TableColumnProps } from "ant-design-vue";
  import type { FormInstance } from "ant-design-vue";
  import { message } from "ant-design-vue";
  import { PlusOutlined } from "@ant-design/icons-vue";
  import type { Dayjs } from "dayjs";
  import dayjs from "dayjs";
  import type { Rule } from "ant-design-vue/es/form";
  interface FormState {
    code: string;
    name: string;
    startTime: Dayjs;
    endTime: Dayjs;
    description: string;
    items: Array<{
      materiel_id: number;
      name: string;
      code: string;
      model: string;
      unit: string;
      sepc: string;
      quantity: number;
    }>;
  }

  interface Materiel {
    id: number;
    name: string;
    code: string;
    model: string;
    unit: string;
    sepc: string;
  }
  const tableRef = ref();
  type TableData = Awaited<ReturnType<typeof query>>["data"]["result"][number];

  const columns = ref<TableColumnProps<TableData>[]>([
    {
      title: "编码",
      dataIndex: "code",
    },
    {
      title: "名称",
      dataIndex: "name",
    },
    {
      title: "时间",
      dataIndex: "time",
      customRender: (opt) => {
        return `${dayjs(opt.record.startTime).format("YYYY-MM-DD")} 至 ${dayjs(
          opt.record.endTime
        ).format("YYYY-MM-DD")}`;
      },
    },
    // {
    //   title: "开始时间",
    //   dataIndex: "startTime",
    //   customRender: (opt) => {
    //     return dayjs(opt.record.startTime).format("YYYY-MM-DD");
    //   },
    // },
    // {
    //   title: "结束时间",
    //   dataIndex: "endTime",
    //   customRender: (opt) => {
    //     return dayjs(opt.record.endTime).format("YYYY-MM-DD");
    //   },
    // },
    {
      title: "描述",
      dataIndex: "description",
    },
    {
      title: "状态",
      dataIndex: "status",
      customRender: (opt) => {
        switch (opt.record.status) {
          case 0:
            return "未发布";
          case 1:
            return "已发布";
          case 2:
            return "已终止";
        }
      },
    },
    {
      title: "完成度",
      dataIndex: "completion",
    },
    {
      title: "操作",
      dataIndex: "action",
    },
  ]);

  const materielColumns = [
    { title: "物料名称", dataIndex: "name" },
    { title: "物料编码", dataIndex: "code" },
    { title: "数量", dataIndex: "quantity" },
    { title: "操作", dataIndex: "action" },
  ];

  const modalOpen = ref<boolean>(false);
  const materielSelectorOpen = ref<boolean>(false);
  const formRef = ref<FormInstance>();
  const formState = reactive<FormState>({
    code: "",
    name: "",
    startTime: dayjs(),
    endTime: dayjs(),
    description: "",
    items: [],
  });

  const rules: Record<string, Rule[]> = {
    code: [{ required: true, message: "请输入编码" }],
    name: [{ required: true, message: "请输入名称" }],
    startTime: [{ required: true, message: "请选择开始时间" }],
    endTime: [{ required: true, message: "请选择结束时间" }],
    items: [
      { required: true, type: "array", min: 1, message: "请添加生产物料" },
    ],
  };

  const query = useApiTrpc().admin.production.queryProductionPlan.query;
  const createPlan = useApiTrpc().admin.production.createProductionPlan.mutate;
  const terminatePlan = useApiTrpc().admin.production.terminatePlan.mutate;
  const publishPlan = useApiTrpc().admin.production.publishPlan.mutate;

  const publishLoading = ref<boolean>(false);
  const createWorkOrderLoading = ref<boolean>(false);

  const viewPlan = reactive({
    visible: false,
    record: {} as any,
    show(record: TableData) {
      viewPlan.visible = true;
      viewPlan.record = record;
    },
  });

  const showMaterielSelector = () => {
    materielSelectorOpen.value = true;
  };

  const onMaterielSelect = (materials: Materiel[]) => {
    materials.forEach((material) => {
      // 避免重复添加
      if (!formState.items.find((item) => item.materiel_id === material.id)) {
        formState.items.push({
          materiel_id: material.id,
          name: material.name,
          code: material.code,
          model: material.model,
          unit: material.unit,
          sepc: material.sepc,
          quantity: 1,
        });
      }
    });
  };

  const removeMateriel = (index: number) => {
    formState.items.splice(index, 1);
  };

  const handleOk = () => {
    createPlan({
      ...formState,
      startTime: formState.startTime.toISOString(),
      endTime: formState.endTime.toISOString(),
    })
      .then(() => {
        message.success("创建成功");
        modalOpen.value = false;
        tableRef.value.query();
        formRef.value?.resetFields();
      })
      .catch((error) => {
        message.error(error.message || "创建失败");
      });
  };

  const handleCancel = () => {
    modalOpen.value = false;
    formRef.value?.resetFields();
  };

  // 生成唯一编码
  const generateUniqueCode = () => {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, "0");
    return `PP${year}${month}${day}${random}`;
  };

  // 表单显示时处理函数
  const handleShow = () => {
    // 重置表单状态
    formState.code = generateUniqueCode();
    formState.name = "";
    formState.startTime = dayjs();
    formState.endTime = dayjs();
    formState.description = "";
    formState.items = [];
  };

  const handleTerminate = async (record: TableData) => {
    try {
      await terminatePlan({ id: record.id });
      message.success("终止成功");
      tableRef.value.query();
    } catch (error: any) {
      message.error(error.message || "终止失败");
    }
  };

  const handlePublish = async () => {
    try {
      publishLoading.value = true;
      await publishPlan({ id: viewPlan.record.id });
      message.success("发布成功");
      viewPlan.record.status = 1; // 更新本地状态
      tableRef.value.query(); // 刷新列表数据
    } catch (error: any) {
      message.error(error.message || "发布失败");
    } finally {
      publishLoading.value = false;
    }
  };

  const handleCreateWorkOrder = async () => {
    try {
      createWorkOrderLoading.value = true;
      // 这里添加创建工单的API调用
      message.success("工单创建成功");
      viewPlan.visible = false; // 关闭抽屉
      tableRef.value.query(); // 刷新列表数据
    } catch (error: any) {
      message.error(error.message || "创建工单失败");
    } finally {
      createWorkOrderLoading.value = false;
    }
  };

  const viewMaterielColumns = [
    {
      title: "物料名称",
      dataIndex: ["Materiel", "name"],
    },
    {
      title: "物料编码",
      dataIndex: ["Materiel", "code"],
    },
    {
      title: "计划数量",
      dataIndex: "quantity",
    },
    {
      title: "完成数量",
      dataIndex: "completed_quantity",
    },
  ];

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0:
        return "blue";
      case 1:
        return "green";
      case 2:
        return "red";
      default:
        return "default";
    }
  };

  const getStatusText = (status: number) => {
    switch (status) {
      case 0:
        return "未发布";
      case 1:
        return "已发布";
      case 2:
        return "已终止";
      default:
        return "未知状态";
    }
  };
</script>
