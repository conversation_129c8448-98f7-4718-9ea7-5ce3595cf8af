<template>
  <!-- <div>test</div> -->
  <NuxtLayout>
    <template #header>
      <a-space>
        <a-button @click="fullscreen()">
          <FullscreenOutlined />全屏显示
        </a-button>
        <a-button @click="goHome"> <HomeFilled />欢迎,{{ name }} </a-button>
        <a-button @click="updatePwd()"> <EditFilled />修改密码 </a-button>
        <a-button @click="logout()"> <LogoutOutlined />退出登录 </a-button>
      </a-space>
    </template>
    <template #menu>
      <manage-navigation @selectMenuItem="handleClick" :items="menuItems">
      </manage-navigation>
    </template>

    <NuxtPage />
    <!-- 新增：修改密码弹窗 -->
    <a-modal
      v-model:open="pwdModalVisible"
      title="修改密码"
      :maskClosable="false"
      @cancel="handlePwdCancel"
    >
      <a-form
        :model="pwdForm"
        :rules="pwdRules"
        ref="pwdFormRef"
        layout="vertical"
      >
        <a-form-item label="原密码" name="oldPassword">
          <a-input-password
            v-model:value="pwdForm.oldPassword"
            placeholder="请输入原密码"
          />
        </a-form-item>
        <a-form-item label="新密码" name="newPassword">
          <a-input-password
            v-model:value="pwdForm.newPassword"
            placeholder="请输入新密码"
          />
        </a-form-item>
        <a-form-item label="重复新密码" name="repeatPassword">
          <a-input-password
            v-model:value="pwdForm.repeatPassword"
            placeholder="请再次输入新密码"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="handlePwdCancel">取消</a-button>
        <a-button type="primary" :loading="pwdLoading" @click="handlePwdSubmit"
          >提交</a-button
        >
      </template>
    </a-modal>
  </NuxtLayout>
</template>
<script setup lang="ts">
  definePageMeta({
    // middleware: ["auth"],
    layout: "manage-frame",
  });
  import {
    HomeFilled,
    HomeOutlined,
    EditFilled,
    LogoutOutlined,
    MailOutlined,
  } from "@ant-design/icons-vue";
  import type { MenuProps, ItemType } from "ant-design-vue";
  import type { MenuInfo } from "ant-design-vue/es/menu/src/interface";
  import type { VueElement } from "vue";
  import type { MenuItem } from "~/components/manage/navigation/index.vue";
  import { useFullScreen } from "~/utils/fullScreen";
  import { getItems } from "~/utils/manage-menu";
  import { ref, reactive } from "vue";
  import { z } from "zod";
  import { message, Modal } from "ant-design-vue";
  import { zodToAntRules } from "~/utils/zodToAntRules";

  // 扩展 Document 接口
  declare global {
    interface Document {
      webkitFullscreenElement?: Element | null;
      msFullscreenElement?: Element | null;
      webkitExitFullscreen?: () => void;
      msExitFullscreen?: () => void;
    }
    interface HTMLElement {
      webkitRequestFullscreen?: () => Promise<void>;
      msRequestFullscreen?: () => Promise<void>;
    }
  }

  const emits = defineEmits<{
    goHome: [];
    logout: [];
    updatePwd: [];
    selectMenuItem: [MenuInfo];
  }>();
  // const menus = ["home"];
  const result = await useApiTrpc().admin.auth.queryUserMenu.query();
  const menus = result.data.Menu;
  const user = await useAuth().getUser();

  const menuItems = getItems(menus);

  const selectedKeys = defineModel<string[]>("selectedKeys", {
    default: () => ["1"],
  });
  const breadcrumb = defineModel<string[]>("breadcrumb", {
    default: () => [],
  });
  //   const openKeys = ref<string[]>(["sub1"]);

  const handleClick: MenuProps["onClick"] = (e) => {
    navigateTo(`/manage/mes/${e.key}`);
  };

  // const user = await useApi().get()("/api/user/loginstatus");

  const name = ref("");
  if (user) {
    name.value = user.name;
  }
  const activeIndex = ref("logo");
  const handleSelect = (key: any, keyPath: any) => {};

  const goHome = () => {
    emits("goHome");
  };

  // 修改密码弹窗相关
  const pwdModalVisible = ref(false);
  const pwdLoading = ref(false);
  const pwdFormRef = ref();
  const pwdForm = reactive({
    oldPassword: "",
    newPassword: "",
    repeatPassword: "",
  });
  const pwdSchema = z
    .object({
      oldPassword: z.string().min(1, "请输入原密码"),
      newPassword: z.string().min(6, "新密码至少6位"),
      repeatPassword: z.string().min(6, "请再次输入新密码"),
    })
    .refine((data) => data.newPassword === data.repeatPassword, {
      message: "两次输入的新密码不一致",
      path: ["repeatPassword"],
    });
  const pwdRules = zodToAntRules(pwdSchema);

  const handlePwdCancel = () => {
    pwdModalVisible.value = false;
    pwdForm.oldPassword = "";
    pwdForm.newPassword = "";
    pwdForm.repeatPassword = "";
    pwdLoading.value = false;
  };

  const handlePwdSubmit = async () => {
    try {
      await pwdFormRef.value.validate();
      pwdLoading.value = true;
      // 预留tRPC接口调用
      const { code, message: msg } =
        await useApiTrpc().admin.user.changePassword.mutate({
          oldPassword: pwdForm.oldPassword,
          newPassword: pwdForm.newPassword,
        });
      if (code) {
        message.success("密码修改成功，请重新登录");
        handlePwdCancel();
        // 可选：自动登出
        await logout();
      } else {
        message.error(msg || "密码修改失败");
      }
    } catch (err: any) {
      if (err?.errors) {
        err.errors.forEach((e: any) => message.error(e.message));
      } else if (err?.message) {
        message.error(err.message);
      } else {
        message.error("表单校验失败");
      }
    } finally {
      pwdLoading.value = false;
    }
  };

  // 修改 updatePwd 方法，弹窗
  const updatePwd = () => {
    pwdModalVisible.value = true;
  };

  const logout = async () => {
    // emits("logout");
    Modal.confirm({
      title: "提示",
      content: "确定要退出登录吗？",
      onOk: async () => {
        const exit = await useApiTrpc().public.auth.logout.query();
        if (exit.code) {
          navigateTo("/manage");
        }
      },
    });
  };
  const fullscreen = () => {
    import("~/utils/fullScreen").then((fullscreen) => {
      useFullScreen()?.toggle();
    });
  };
</script>
<style scoped>
  .my-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 16px;
  }

  .el-menu {
    --el-menu-bg-color: #207dc3;
    --el-menu-text-color: #fff;
    /* --el-menu-active-color: #fff; */
  }

  .el-menu--horizontal > .el-menu-item:nth-child(1) {
    margin-right: auto;
  }
</style>
