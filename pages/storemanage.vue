<template>
  <client-only>
    <NuxtPage></NuxtPage>
    <LoginDialog
      v-model="loginDialog.visible"
      v-model:status="status"
    ></LoginDialog>
  </client-only>
</template>
<script lang="ts" setup>
  definePageMeta({
    middleware: ["auth"],
  });
  // const { $client } = useNuxtApp();
  const { $emitter } = useNuxtApp();
  const status = ref(false);
  const loginDialog = reactive({
    visible: false,
    open: () => {
      loginDialog.visible = true;
    },
  });
  $emitter.on("changeLoginBoxStatus", (status: boolean) => {
    loginDialog.visible = status;
  });
  $emitter.on("loginFailed", (msg: string) => {
    status.value = false;
    message.error(msg);
  });
  $emitter.on("loginSuccessed", (data: any) => {});
</script>
<style>
  .ant-modal div[aria-hidden="true"] {
    display: none !important;
  }
</style>
