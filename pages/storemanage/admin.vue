<template>
  <manage-base-frame
    v-model:breadcrumb="breadcrumb"
    @select-menu-item="handleSelectMenuItem"
  >
    <template #menuItem>
      <a-menu-item key="0">首页</a-menu-item>
      <a-menu-item-group title="用户管理">
        <!-- <a-menu-item key="authmgr">权限管理</a-menu-item> -->
        <a-menu-item key="usermgr">用户管理</a-menu-item>
        <a-menu-item key="customermgr" :disabled="true">客户管理</a-menu-item>
      </a-menu-item-group>
      <a-menu-item-group title="仓库管理">
        <a-menu-item key="warehousemgr">仓库管理</a-menu-item>
      </a-menu-item-group>
      <a-menu-item-group title="采购管理">
        <a-menu-item key="warehousemgr" :disabled="true">采购管理</a-menu-item>
      </a-menu-item-group>
      <a-menu-item-group title="物料管理">
        <a-menu-item key="materielmgr">物料管理</a-menu-item>
        <a-menu-item key="materieltypemgr">物料分类管理</a-menu-item>
        <!-- <a-menu-item key="bommgr">物料清单管理</a-menu-item> -->
      </a-menu-item-group>
      <a-menu-item-group title="审核管理">
        <a-menu-item key="3-1" :disabled="true">采购审核</a-menu-item>
        <a-menu-item key="3-2" :disabled="true">销售审核</a-menu-item>
        <a-menu-item key="3-3" :disabled="true">退货审核</a-menu-item>
      </a-menu-item-group>
      <a-menu-item-group title="生产管理">
        <a-menu-item key="productiontask">生产工单</a-menu-item>
        <a-menu-item key="warehousemgr">生产报工</a-menu-item>
      </a-menu-item-group>
      <a-menu-item-group title="质量管理">
        <a-menu-item key="materielmgr">常见缺陷</a-menu-item>
      </a-menu-item-group>
      <a-menu-item-group title="排班管理">
        <a-menu-item key="materielmgr">人员管理</a-menu-item>
        <a-menu-item key="materielmgr">班组管理</a-menu-item>
      </a-menu-item-group>
      <a-menu-item-group title="报表管理">
        <a-menu-item key="materielmgr">员工绩效</a-menu-item>
        <a-menu-item key="materielmgr">产量统计</a-menu-item>
      </a-menu-item-group>
    </template>
    <nuxt-page></nuxt-page>
  </manage-base-frame>
</template>
<script lang="ts" setup>
  const { $emitter } = useNuxtApp();
  const breadcrumb = ref(["首页"]);
  const test = () => {
    $emitter.emit("changeLoginBoxStatus", true);
  };
  const handleSelectMenuItem = (e: any) => {
    switch (e.key) {
      // 首页
      case "0":
        breadcrumb.value = ["首页"];
        navigateTo("/storemanage/admin");
        break;
      // 用户管理
      case "usermgr":
        breadcrumb.value = ["首页", "用户管理"];
        navigateTo("/storemanage/admin/user");
        break;
      // 权限管理
      case "authmgr":
        breadcrumb.value = ["首页", "用户管理", "权限管理"];
        navigateTo("/storemanage/admin/auth");
        break;
      // 物料管理
      case "materielmgr":
        breadcrumb.value = ["首页", "物料管理"];
        navigateTo("/storemanage/admin/materiel");
        break;
      // 仓库管理
      case "warehousemgr":
        breadcrumb.value = ["首页", "仓库管理"];
        navigateTo("/storemanage/admin/warehouse");
        break;
      // 物料清单管理
      case "bommgr":
        breadcrumb.value = ["首页", "物料管理", "物料清单管理"];
        navigateTo("/storemanage/admin/bom");
        break;
      // 物料类型管理
      case "materieltypemgr":
        breadcrumb.value = ["首页", "物料管理", "物料类型管理"];
        navigateTo("/storemanage/admin/materiel/type");
        break;
    }
  };
</script>
