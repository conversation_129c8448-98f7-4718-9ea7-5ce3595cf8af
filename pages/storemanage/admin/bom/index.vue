<template>
  <a-card title="物料清单管理">
    <template #extra>
      <a-space>
        <a-button @click="handleAdd">新建</a-button>
      </a-space>
    </template>
    <ui-storemanage-table
      :columns="columns"
      :query="useApiFetch.queryBom"
      ref="tableRef"
      rowKey="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'product_sku'">
          <a-tooltip>
            <template #title>
              <div>
                <p>SKU名称: {{ record.product_sku.title }}</p>
                <p>编码: {{ record.product_sku.Product.code }}</p>
                <p>型号: {{ record.product_sku.Product.model }}</p>
                <p>规格: {{ record.product_sku.specification }}</p>
                <p>单位: {{ record.product_sku.unit }}</p>
              </div>
            </template>
            {{ record.product_sku.Product.title }}
          </a-tooltip>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a @click="">编辑</a>
          </a-space>
        </template>
      </template>
    </ui-storemanage-table>

    <a-drawer
      :title="drawerTitle"
      :width="720"
      :visible="showDrawer"
      @close="onClose"
      :bodyStyle="{ paddingBottom: '80px' }"
    >
      <a-form layout="vertical" :model="formState" ref="formEl" :rules="rules">
        <a-form-item label="物料清单名称" name="title">
          <a-input
            v-model:value="formState.title"
            placeholder="请输入物料清单名称"
          />
        </a-form-item>

        <a-form-item label="物料清单描述" name="description">
          <a-textarea
            v-model:value="formState.description!"
            placeholder="请输入物料清单描述"
          />
        </a-form-item>
        <a-form-item label="产成品" name="product_sku_id">
          <item-materiel-selector
            v-model:materielid="formState.product_sku_id"
          />
        </a-form-item>
        <a-form-item label="物料清单版本" name="version">
          <a-input
            v-model:value="formState.version"
            placeholder="请输入版本号"
          />
        </a-form-item>
        <a-form-item name="materials">
          <a-divider>物料列表</a-divider>
        </a-form-item>
        <list-materiel-shoppingcar
          v-model:materials="formState.materials"
        ></list-materiel-shoppingcar>
      </a-form>

      <template #footer>
        <a-space>
          <a-button @click="onClose">取消</a-button>
          <a-button type="primary" @click="onSubmit" :loading="submitting"
            >保存</a-button
          >
        </a-space>
      </template>
    </a-drawer>
  </a-card>
</template>

<script setup lang="ts">
  import type { Rule } from "ant-design-vue/es/form";
  import { message } from "ant-design-vue";

  const columns = [
    { title: "物料清单名称", dataIndex: "title", key: "title" },
    { title: "生产物料", dataIndex: "product_sku", key: "product_sku" },
    { title: "版本", dataIndex: "version", key: "version" },
    { title: "创建时间", dataIndex: "createAt", key: "createAt" },
    { title: "更新时间", dataIndex: "updateAt", key: "updateAt" },
    { title: "操作", key: "action" },
  ];

  const tableRef = ref();
  const submitting = ref(false);
  const showDrawer = ref(false);
  const drawerTitle = ref("新增物料清单");
  const formState = ref<API.BomCreateInput>({
    title: "",
    materials: [] as { id: number; quantity: number }[],
    product_sku_id: 0,
    version: "",
    description: "",
  });

  const rules: Record<string, Rule[]> = reactive({
    title: [{ required: true, message: "请输入物料清单名称" }],
    product_sku_id: [{ required: true, message: "请选择产成品" }],
    materials: [{ required: true, message: "请添加至少一个物料" }],
    version: [{ required: true, message: "请输入版本号" }],
  });

  const handleAdd = () => {
    navigateTo("/storemanage/admin/bom/create");
    // drawerTitle.value = "新增物料清单";
    // formState.value = {
    //   title: "",
    //   materials: [],
    //   product_sku_id: 0,
    //   version: "",
    //   description: "",
    // };
    // showDrawer.value = true;
  };

  const onClose = () => {
    showDrawer.value = false;
    formState.value = {
      title: "",
      materials: [],
      product_sku_id: 0,
      version: "",
      description: "",
    };
  };

  const formEl = ref();

  const onSubmit = async () => {
    try {
      await formEl.value.validate();
    } catch (error) {
      console.log(error);
      message.error("操作失败");
    } finally {
      nextTick(() => {
        tableRef.value?.query();
      });
      submitting.value = false;
    }
  };
</script>
