<template>
  <NuxtLayout>
    <a-layout>
      <a-layout-content style="padding: 10px">
        <a-card title="主页">
          <!-- {{ hello.data }}
          {{ hello2.data }} -->
          <a-image :src="captcha" :preview="false" />
          <a-button @click="captchaRequest.refresh()">刷新</a-button>
          <button class="btn btn-sm btn-primary">Success</button>
          <!-- {{ captchaText }} -->
        </a-card>
      </a-layout-content>
    </a-layout>
  </NuxtLayout>
</template>
<script lang="ts" setup>
  // const captcha = await $client.query("public.login.captcha", {})
  const trpc = useApiTrpc();
  // const hello = await useApiTprc().public.showCxt.query();
  // const hello2 = await useApiTprc().public.showCxt.useQuery();
  const captchaRequest = await useApiTrpc().public.auth.captcha.useQuery({});
  const captcha = computed(() => {
    // console.log(captchaRequest.data);
    if (captchaRequest.data.value) {
      return captchaRequest.data.value.data;
    }
    return "";
  });
  // const hello2 = await useApiTprc().public.showCxt.useQuery();
  // await useAsyncData("testAsyncData", async () => {
  //   console.log("testAsyncData");
  //   return "testAsyncData";
  // });
  // const captcha = computed(() => {
  //   if (verifyCodeSvg.value) {
  //     const b64 = window.btoa(
  //       decodeURIComponent(encodeURIComponent(verifyCodeSvg.value))
  //     );
  //     return `data:image/svg+xml;base64,${b64}`;
  //   }
  //   return "";
  // });
  // const captchaText = await $client.query("login.login", {
  //   username: "admin",
  //   password: "123456",
  // });
</script>
