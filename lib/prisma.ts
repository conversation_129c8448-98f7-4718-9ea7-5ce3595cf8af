import { Prisma, PrismaClient } from "@prisma/client";
import Crypto<PERSON><PERSON> from "crypto-js";
import md5 from "md5";

const prismaClientSingleton = () => {
  return new PrismaClient().$extends({
    model: {
      $allModels: {
        async findManyWithCount<T, A>(
          this: T,
          args?: Prisma.Exact<A, Prisma.Args<T, "findMany">>
        ): Promise<{ result: Prisma.Result<T, A, "findMany">; total: number }> {
          const context = Prisma.getExtensionContext(this);
          const result = await (context as any).findMany(args);
          const total = (await (context as any).count({
            where: args?.where,
          })) as number;
          return { result, total };
        },
      },
      user: {
        async login(username: string, password: string) {
          const user = await prisma.user.findFirst({
            omit: {
              password: true,
            },
            include: {
              role: true,
            },
            where: {
              username: username,
              password: CryptoJS.SHA256(password).toString(),
            },
          });
          return user;
        },
      },
      // menu: {
      //   async getMenu(user_id?: number) {
      //     if (user_id) {
      //       const user = await prisma.user.findUnique({
      //         where: {
      //           id: user_id,
      //         },
      //       });
      //       const menu = await prisma.roleMenu.findMany({
      //         include: {
      //           menu: true,
      //         },
      //         where: {
      //           role_id: user?.role_id,
      //         },
      //       });
      //       return menu;
      //     } else {
      //       return null;
      //     }
      //   },
      // },
    },
    query: {
      user: {
        async create({ model, operation, args, query }) {
          args.data.password = CryptoJS.SHA256(args.data.password).toString();
          return await query(args);
        },
        async update({ model, operation, args, query }) {
          if (args.data.password) {
            args.data.password = CryptoJS.SHA256(
              args.data.password as string
            ).toString();
          }
          return await query(args);
        },
        async createMany({ model, operation, args, query }) {
          if (Array.isArray(args.data)) {
            args.data.forEach((item) => {
              item.password = CryptoJS.SHA256(
                item.password as string
              ).toString();
            });
          } else {
            args.data.password = CryptoJS.SHA256(
              args.data.password as string
            ).toString();
          }
          return await query(args);
        },
        async updateMany({ model, operation, args, query }) {
          if (Array.isArray(args.data)) {
            args.data.forEach((item) => {
              item.password = CryptoJS.SHA256(
                item.password as string
              ).toString();
            });
          } else {
            args.data.password = CryptoJS.SHA256(
              args.data.password as string
            ).toString();
          }
          return await query(args);
        },
      },
    },
  });
};

declare const globalThis: {
  prismaGlobal: ReturnType<typeof prismaClientSingleton>;
} & typeof global;

export const prisma = globalThis.prismaGlobal ?? prismaClientSingleton();
export { Prisma, PrismaClient } from "@prisma/client";
if (process.env.NODE_ENV !== "production") globalThis.prismaGlobal = prisma;
